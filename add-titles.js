#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 为src/content/manuals下所有md文件的updateDate前一行添加title字段
 * title内容为正文第一个#标签的内容
 */

const manualsDir = path.join(__dirname, 'src/content/manuals');

function extractTitleFromContent(content) {
    // 查找第一个 # 标题
    const lines = content.split('\n');
    for (const line of lines) {
        const trimmedLine = line.trim();
        if (trimmedLine.startsWith('# ')) {
            // 提取标题内容，去掉 # 和前后空格
            return trimmedLine.substring(2).trim();
        }
    }
    return null;
}

function processMarkdownFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n');
        
        // 查找updateDate行的索引
        let updateDateIndex = -1;
        for (let i = 0; i < lines.length; i++) {
            if (lines[i].trim().startsWith('updateDate:')) {
                updateDateIndex = i;
                break;
            }
        }
        
        if (updateDateIndex === -1) {
            console.log(`⚠️  ${path.basename(filePath)}: 未找到updateDate字段`);
            return false;
        }
        
        // 检查是否已经有title字段
        if (updateDateIndex > 0 && lines[updateDateIndex - 1].trim().startsWith('title:')) {
            console.log(`✅ ${path.basename(filePath)}: 已存在title字段，跳过`);
            return false;
        }
        
        // 提取标题
        const title = extractTitleFromContent(content);
        if (!title) {
            console.log(`⚠️  ${path.basename(filePath)}: 未找到第一个#标题`);
            return false;
        }
        
        // 在updateDate前一行插入title
        const titleLine = `title: ${title}`;
        lines.splice(updateDateIndex, 0, titleLine);
        
        // 写回文件
        const newContent = lines.join('\n');
        fs.writeFileSync(filePath, newContent, 'utf8');
        
        console.log(`✅ ${path.basename(filePath)}: 已添加title: ${title}`);
        return true;
        
    } catch (error) {
        console.error(`❌ 处理文件 ${path.basename(filePath)} 时出错:`, error.message);
        return false;
    }
}

function main() {
    console.log('开始处理src/content/manuals目录下的markdown文件...\n');
    
    if (!fs.existsSync(manualsDir)) {
        console.error(`❌ 目录不存在: ${manualsDir}`);
        process.exit(1);
    }
    
    const files = fs.readdirSync(manualsDir);
    const mdFiles = files.filter(file => file.endsWith('.md'));
    
    if (mdFiles.length === 0) {
        console.log('未找到任何markdown文件');
        return;
    }
    
    console.log(`找到 ${mdFiles.length} 个markdown文件\n`);
    
    let processedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    
    for (const file of mdFiles) {
        const filePath = path.join(manualsDir, file);
        const result = processMarkdownFile(filePath);
        
        if (result === true) {
            processedCount++;
        } else if (result === false) {
            skippedCount++;
        } else {
            errorCount++;
        }
    }
    
    console.log('\n处理完成！');
    console.log(`✅ 成功处理: ${processedCount} 个文件`);
    console.log(`⏭️  跳过: ${skippedCount} 个文件`);
    console.log(`❌ 错误: ${errorCount} 个文件`);
}

// 运行脚本
if (require.main === module) {
    main();
}

module.exports = { processMarkdownFile, extractTitleFromContent };
