apiVersion: apps/v1
kind: Deployment
metadata:
  name: qcc-rover-landing-web
  labels:
    app: qcc-rover-landing-web
    version: v1
spec:
  minReadySeconds: 5
  revisionHistoryLimit: 5
  progressDeadlineSeconds: 60
  strategy:
    rollingUpdate:
      maxSurge: 4
      maxUnavailable: 50%
    type: RollingUpdate
  selector:
    matchLabels:
      app: qcc-rover-landing-web
      version: v1
  template:
    metadata:
      annotations:
        prometheus.io/scrape: 'true'
      labels:
        app: qcc-rover-landing-web
        version: v1
    spec:
      containers:
        - name: qcc-rover-landing-web
          image: IMAGE_NAME:IMAGE_TAG
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 80
          livenessProbe:
            httpGet:
              path: /nginx-health
              port: 80
            initialDelaySeconds: 10
            timeoutSeconds: 10
            periodSeconds: 30
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /nginx-health
              port: 80
            initialDelaySeconds: 10
            timeoutSeconds: 5
            periodSeconds: 15
            failureThreshold: 3
          resources:
            limits:
              cpu: 1000m
              memory: 1000Mi
            requests:
              cpu: 100m
              memory: 128Mi
          volumeMounts:
            - name: nginx-proxy-config
              mountPath: /etc/nginx/nginx.conf
              subPath: nginx.conf.rover.landing
      volumes:
        - name: nginx-proxy-config
          configMap:
            name: nginx-conf-rover
