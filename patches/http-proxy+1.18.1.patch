diff --git a/node_modules/http-proxy/lib/http-proxy/common.js b/node_modules/http-proxy/lib/http-proxy/common.js
index 6513e81..e93d298 100644
--- a/node_modules/http-proxy/lib/http-proxy/common.js
+++ b/node_modules/http-proxy/lib/http-proxy/common.js
@@ -1,6 +1,5 @@
 var common   = exports,
     url      = require('url'),
-    extend   = require('util')._extend,
     required = require('requires-port');
 
 var upgradeHeader = /(^|,)\s*upgrade\s*($|,)/i,
@@ -40,10 +39,10 @@ common.setupOutgoing = function(outgoing, options, req, forward) {
   );
 
   outgoing.method = options.method || req.method;
-  outgoing.headers = extend({}, req.headers);
+  outgoing.headers = Object.assign({}, req.headers);
 
   if (options.headers){
-    extend(outgoing.headers, options.headers);
+    Object.assign(outgoing.headers, options.headers);
   }
 
   if (options.auth) {
diff --git a/node_modules/http-proxy/lib/http-proxy/index.js b/node_modules/http-proxy/lib/http-proxy/index.js
index 977a4b3..76b5def 100644
--- a/node_modules/http-proxy/lib/http-proxy/index.js
+++ b/node_modules/http-proxy/lib/http-proxy/index.js
@@ -1,5 +1,4 @@
 var httpProxy = module.exports,
-    extend    = require('util')._extend,
     parse_url = require('url').parse,
     EE3       = require('eventemitter3'),
     http      = require('http'),
@@ -19,7 +18,7 @@ httpProxy.Server = ProxyServer;
  *    // => [Function]
  *
  * @param {String} Type Either 'ws' or 'web'
- * 
+ *
  * @return {Function} Loader Function that when called returns an iterator for the right passes
  *
  * @api private
@@ -47,9 +46,9 @@ function createRightProxy(type) {
         args[cntr] !== res
       ) {
         //Copy global options
-        requestOptions = extend({}, options);
+        requestOptions = Object.assign({}, options);
         //Overwrite with request options
-        extend(requestOptions, args[cntr]);
+        Object.assign(requestOptions, args[cntr]);
 
         cntr--;
       }
