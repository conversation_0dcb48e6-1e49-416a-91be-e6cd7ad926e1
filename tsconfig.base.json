{
  "compilerOptions": {
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "Node",
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "jsx": "preserve",
    "strict": false,
    "noImplicitThis": true,
    "experimentalDecorators": true,
    "noImplicitAny": false,
    "skipLibCheck": true,
    "useDefineForClassFields": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    // "preserveValueImports": true,
    // "importsNotUsedAsValues": "error",
    "preserveSymlinks": true // pnpm support
  }
}
