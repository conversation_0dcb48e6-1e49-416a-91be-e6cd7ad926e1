import fs from 'node:fs';
import { execSync } from 'node:child_process';

function parseArgs(argv) {
  const args = { files: [], date: null };
  for (const a of argv.slice(2)) {
    if (a.startsWith('--date=')) args.date = a.slice('--date='.length);
    else args.files.push(a);
  }
  return args;
}

function isValidDate(dateStr) {
  // 基本校验 YYYY-MM-DD
  if (!/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) return false;
  const d = new Date(dateStr);
  if (Number.isNaN(d.getTime())) return false;
  // 防止 2025-02-31 这类非法日
  const [y, m, dd] = dateStr.split('-').map(Number);
  return d.getUTCFullYear() === y && d.getUTCMonth() + 1 === m && d.getUTCDate() === dd;
}

function listStagedManualMd() {
  const out = execSync('git diff --name-only --cached -- src/content/manuals', { encoding: 'utf8' });
  return out
    .split('\n')
    .map(s => s.trim())
    .filter(s => s && s.endsWith('.md') && s.startsWith('src/content/manuals/'));
}

function updateFile(filePath, dateStr) {
  const orig = fs.readFileSync(filePath, 'utf8');

  // Front Matter 必须在文件最开头
  const fmMatch = orig.match(/^---\n([\s\S]*?)\n---\n?/);
  let updated = orig;

  if (fmMatch) {
    const fullBlock = fmMatch[0];
    let header = fmMatch[1];

    if (/^updateDate:\s*/m.test(header)) {
      header = header.replace(/^updateDate:\s*.*/m, `updateDate: ${dateStr}`);
    } else {
      // 放在 header 顶部
      header = `updateDate: ${dateStr}\n` + header;
    }

    const replaced = `---\n${header}\n---\n`;
    updated = orig.replace(fullBlock, replaced);
  } else {
    // 无 Front Matter，按要求创建最小结构
    updated = `---\nupdateDate: ${dateStr}\n---\n\n` + orig;
  }

  if (updated !== orig) {
    fs.writeFileSync(filePath, updated, 'utf8');
    return true;
  }
  return false;
}

function ensureFilesExist(files) {
  return files.filter(f => fs.existsSync(f));
}

function main() {
  const { files: cliFiles, date } = parseArgs(process.argv);
  const dateStr = date || process.env.RELEASE_DATE || '';

  if (!dateStr) {
    console.error('Error: 请通过 --date=YYYY-MM-DD 或环境变量 RELEASE_DATE 提供日期');
    process.exit(2);
    return;
  }
  if (!isValidDate(dateStr)) {
    console.error(`Error: 非法日期格式 "${dateStr}"，应为 YYYY-MM-DD`);
    process.exit(2);
    return;
  }

  let targets = cliFiles.length ? cliFiles : listStagedManualMd();
  targets = ensureFilesExist(targets);

  if (!targets.length) {
    console.log('未发现需处理的文件（请确保已暂存 src/content/manuals 下的 .md 改动）');
    return;
  }

  const touched = [];
  for (const f of targets) {
    try {
      if (updateFile(f, dateStr)) touched.push(f);
    } catch (e) {
      console.error(`Failed to update ${f}:`, e.message);
      process.exitCode = 1;
    }
  }

  if (touched.length) {
    // 重新加入暂存区，确保进入本次提交
    execSync(`git add ${touched.map(s => `"${s}"`).join(' ')}`, { stdio: 'inherit' });
    console.log(`已更新并暂存 ${touched.length} 个文件`);
  } else {
    console.log('无需更新（updateDate 已符合要求或无变化）');
  }
}

main();
