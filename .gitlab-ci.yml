include:
  - project: 'kezhaozhao/tools/qcc-deployment'
    ref: static
    file: '/gitlab/template.frontend.v3.yml'
variables:
  DEPENDENCIES_IMAGE: 'harbor-in.greatld.com/kezhaozhao/node:22.17.0-slim'
  NS_RELEASE: 'release'
  NS_PROD: 'rover'
  CLUSTER: 'rover'
  OSS_PROJECT_FOLDER: rover/$CI_PIPELINE_ID
  MOBILE_NUMBERS: '17612542237'
  SERVICE_IMAGE_BASE: $HARBOR_REPO/$CI_PROJECT_NAME:base-1.2.1
  NODE_VERSION: 'v22.17.0'

.tags_job:
  tags:
    - runner_frontend_group1

oss:
  extends:
    - .oss_rover
  only:
    - master
