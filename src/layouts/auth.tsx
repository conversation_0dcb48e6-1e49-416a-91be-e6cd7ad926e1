import { defineComponent } from '@nuxtjs/composition-api';
import AppHeader from './widgets/app-header';
import AppFooter from './widgets/app-footer';
import styles from './auth/styles.module.less';

const AuthLayout = defineComponent({
  name: 'AuthLayout',
  render() {
    return (
      <div class={styles.container}>
        <div class={styles.header}>
          <AppHeader theme="dark" showActions={false} isFixed={false} />
        </div>
        <div class={styles.body}>
          <nuxt />
        </div>
        <div class={styles.footer}>
          <AppFooter />
        </div>
      </div>
    );
  },
});

export default AuthLayout;
