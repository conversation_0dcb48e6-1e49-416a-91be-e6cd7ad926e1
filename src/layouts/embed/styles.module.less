@header-height: 52px;

.container {
  background: #f7f7f7;
  max-height: 100vh;
  overflow: hidden;

  .header {
    z-index: 30;
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    height: @header-height;
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.08);
    overflow: hidden;
  }
  .body {
    margin-top: @header-height;
    height: calc(100vh - @header-height);
    overflow: auto;

    &::-webkit-scrollbar {
      width: 10px;
      height: 10px
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(50, 50, 50, .25);
      border: 2px solid rgba(0, 0, 0, 0);
      border-radius: 10px;

      &:hover {
        background-color: rgba(50, 50, 50, .5)
      }
    }
  }
}
