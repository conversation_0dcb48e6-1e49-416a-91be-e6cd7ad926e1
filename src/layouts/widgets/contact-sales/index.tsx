import { defineComponent, ref, unref } from 'vue';
import { Button } from 'ant-design-vue';
import styles from './styles.module.less';

const ContactSales = defineComponent({
  name: 'ContactSales',
  emits: ['contact'],
  setup(props, { emit }) {
    const expand = ref(true);
    const handleToggleExpand = () => {
      expand.value = !unref(expand);
    };
    const handleOpenChat = () => {
      emit('contact');
    };
    return {
      expand,
      handleToggleExpand,
      handleOpenChat,
    };
  },
  render() {
    return (
      <div
        class={{
          [styles.container]: true,
          [styles.expand]: !this.expand,
        }}
      >
        <div
          v-show={this.expand}
          class={styles.close}
          onClick={this.handleToggleExpand}
          domPropsInnerHTML={require(`@/assets/icons/icon-guanbi.svg?raw`)}
        ></div>

        <div class={styles.action} v-show={!this.expand} onClick={this.handleToggleExpand}>
          <div
            class={styles.circle}
            domPropsInnerHTML={require(`@/assets/icons/icon-lianxiwomen.svg?raw`)}
          ></div>
          <div class={[styles.wave, styles.default]} />
          <div class={[styles.wave, styles.large]} />
        </div>

        <div class={styles.body} v-show={this.expand}>
          <div class={styles.header}>
            <h3>风险早发现 信任零距离</h3>
            <h4>准入排查 · 客商巡检 · 关系排查</h4>
          </div>
          <div class={styles.content}>
            <div class={styles.qrcode}>
              <img src={require('./images/qrcode-wechat.jpg')} width="100" height="100" />
            </div>
            <div class={styles.info}>扫码咨询</div>
          </div>
          <div class={styles.footer}>
            <div class={styles.phone}>
              {/* <QIcon class={styles.icon} type="icon-dianhua" /> */}
              <i domPropsInnerHTML={require(`@/assets/icons/icon-dianhua.svg?raw`)}></i>
              <strong>************</strong>
            </div>
            <div class={styles.chat}>
              <Button type="primary" onClick={this.handleOpenChat}>
                在线咨询
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  },
});

export default ContactSales;
