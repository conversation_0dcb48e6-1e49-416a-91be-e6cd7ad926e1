.container {
  position: fixed;
  right: 10px;
  bottom: 50px;
  z-index: 101;
  // overflow: hidden;
  //
  .action {
    cursor: pointer;
    display: block;
    position: relative;
    width: 50px;
    height: 50px;

    > div {
      position: absolute;
      inset: 0;
      border-radius: 50%;
    }

    .circle {
      font-size: 20px;
      border: 4px solid #128bed;
      padding: 13px;
      z-index: 10;
      background-color: #f6fbfe;
      color: #128bed;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .wave {
      background: #128bed;

      &.default {
        animation: scale-up-center 1.3s linear 0.1s infinite normal none;
        z-index: 5;
      }

      &.large {
        animation: scale-up-center-large 1.3s linear 0.1s infinite normal none;
        z-index: 1;
      }
    }

    &:hover {
      .circle {
        background-color: #e2f1fd;
      }
    }

    &::before {
      content: '';
      display: block;
    }
  }

  .body {
    width: 228px;
    border-radius: 10px;
    padding-top: 15px;
    background: #fff;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);

    > *:not(:last-child) {
      margin-bottom: 15px;
    }
  }

  .close {
    position: absolute;
    top: -24px;
    right: 0;
    width: 16px;
    height: 16px;
    // background: url(./images/icon-close.svg) no-repeat 50% 50%;
    // background-size: 16px 16px;
    // transform: translateY(-24px);
    cursor: pointer;
    color: #bbb;

    &:hover {
      color: #999;
    }
  }

  .header {
    text-align: center;

    h3 {
      margin: 0;
      font-size: 16px;
      line-height: 24px;
      font-weight: 700;
      color: #333;
      letter-spacing: 2px;
    }

    h4 {
      margin: 5px 0 0;
      font-size: 13px;
      line-height: 20px;
      font-weight: 400;
      color: #999;
    }
  }

  .content {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;

    .info {
      font-size: 12px;
      line-height: 18px;
      color: #999;
    }

    .qrcode {
      margin-bottom: 10px;
      display: inline-block;
      padding: 4px;
      border: 1px solid #eee;
      border-radius: 5px;
      font-size: 0;
    }

    img {
      display: block;
      margin: 0 auto;
    }
  }

  .footer {
    background: #f3f9fd;
    padding: 15px 0;
    border-bottom-right-radius: 10px;
    border-bottom-left-radius: 10px;
    overflow: hidden;
    text-align: center;

    > *:not(:last-child) {
      margin-bottom: 10px;
    }

    .phone {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      color: #ff722d;
      line-height: 26px;

      .icon {
        font-size: 16px;
      }

      > *:not(:last-child) {
        margin-right: 8px;
      }
    }
  }
}

@keyframes scale-up-center {
  0% {
    opacity: 0.25;
    transform: scale(1);
  }

  50% {
    opacity: 0.15;
  }

  100% {
    opacity: 0;
    transform: scale(1.3);
  }
}

@keyframes scale-up-center-large {
  0% {
    opacity: 0.1;
    transform: scale(1.2);
  }

  50% {
    opacity: 0.05;
  }

  100% {
    opacity: 0;
    transform: scale(1.7);
  }
}
