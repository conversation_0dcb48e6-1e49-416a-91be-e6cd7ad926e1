@import '@/assets/styles/token.less';

.container {
  height: 52px;
  z-index: 1;
  padding: 0 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  will-change: background-color, box-shadow;
  transition: background-color 0.25s ease-in-out, box-shadow 0.25s ease-in-out;

  &.fixed {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
  }

  .menu {
    display: flex;
    align-items: center;
    color: #333;
    font-size: 14px;
  }

  .user {
    color: @base-color-blue-500;
    transition: color 0.25s ease-in-out;

    * + * {
      margin-left: 10px;
    }

    a,
    .link {
      cursor: pointer;
      transition: 0.3s ease color;
    }
  }

  .brand {
    display: flex;
    align-items: center;
    cursor: pointer;

    > *:not(:last-child) {
      margin-right: 6px;
    }
  }

  .divider {
    height: 15px;
  }

  // Button
  .button + .button {
    margin-left: 10px;
  }

  .button {
    outline: 0;
    font-size: 14px;
    cursor: pointer;
    padding: 0 10px;
    min-width: 80px;
    border-width: 1px;
    border-style: solid;
    border-radius: 2px;
    height: 32px;
    will-change: color, background-color, border-color;
    transition: color 0.2s ease, background-color 0.2s ease-in-out, border-color 0.1s ease-in-out;

    &.default {
      border-color: currentcolor;
      color: @base-color-blue-500;
      background-color: @base-color-white;

      &:hover {
        background-color: @base-color-blue-500;
        border-color: transparent;
        color: @base-color-white;
      }
    }

    &.primary {
      background-color: @base-color-blue-500;
      border-color: transparent;
      color: @base-color-white;

      &:hover {
        background-color: @base-color-blue-600;
        // border-color: @base-color-blue-600;
      }
    }
  }

  // Theme
  &.light {
    background-color: @base-color-white;
    box-shadow: 0 4px 5px 0 rgba(92, 92, 92, 0.1);

    .brand {
      color: #222;
    }

    .user,
    .link,
    .button.default {
      color: @base-color-blue-500;
    }

    .button.default:hover {
      color: @base-color-white;
    }
  }

  &.dark {
    background-color: transparent;

    .button.default {
      color: @base-color-blue-500;
      border-color: transparent;

      &:hover {
        color: @base-color-white;
      }
    }

    .brand,
    .user,
    .link {
      color: @base-color-white;
    }
  }
}
