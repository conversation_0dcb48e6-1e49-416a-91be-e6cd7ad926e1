import { defineComponent } from 'vue';
import styles from './app-header-pro.module.less';
import env from '@/config/env';


const Logo = defineComponent({
  functional: true,
  props: {
    src: {
      type: String,
      required: false,
    },
    href: {
      type: String,
      default: '/',
    },
  },
  render(h, { props, slots }) {
    const { default: children } = slots();
    const handleEnter = (ev) => {
      ev.stopPropagation()
      window.location.href = env.KYS_ENTRY;
    };
    return (
      <a
        class={{
          [styles.brand]: true,
          [styles.disabled]: !props.href,
        }}
        onClick={handleEnter}
      >
        {props.src ? <img slot="logo" src={props.src} height="20" /> : children}
      </a>
    );
  },
});

export default Logo;
