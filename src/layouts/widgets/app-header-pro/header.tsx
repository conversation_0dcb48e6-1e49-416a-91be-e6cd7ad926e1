import { defineComponent } from 'vue';

import styles from './app-header-pro.module.less';

const AppHeader = defineComponent({
  functional: true,
  render(h, ctx) {
    const slots = ctx.slots();
    return (
      <div class={styles.container}>
        <div class={styles.wing}>
          {slots.logo}
          {slots.navigation}
        </div>
        <div class={styles.wing}>
          <div class={styles.extra}>{slots.extra}</div>
          <div class={styles.menu}>{slots.quick}</div>
          <div class={styles.user}>{slots.user}</div>
        </div>
      </div>
    );
  },
});

export default AppHeader;
