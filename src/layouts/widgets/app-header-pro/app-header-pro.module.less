@header-height: 52px;

.container {
  min-height: @header-height;
  padding: 0 15px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .wing {
    display: flex;
    align-items: center;

    > div:not(:last-child) {
      margin-right: 20px;
    }
  }

  .brand {
    margin-right: 60px !important;
    color: #222;
    display: flex;

    &.disabled {
      pointer-events: none;
    }

    &:hover {
      color: currentcolor !important;
    }
  }

}
