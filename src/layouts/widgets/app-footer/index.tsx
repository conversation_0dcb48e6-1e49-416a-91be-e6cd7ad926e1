import { defineComponent } from 'vue';
import IconPolice from './assets/icon-police.png';
import styles from './styles.module.less';

const Footer = defineComponent({
  functional: true,
  render() {
    return (
      <div class={styles.container}>
        <div class={styles.wrapper}>
          <div class={styles.info}>
            <dl class={styles.block}>
              <dt>关于我们</dt>
              <dd>
                <nuxt-link to="/portal/about-us">关于我们</nuxt-link>
              </dd>
              <dd>
                <nuxt-link to="/portal/terms-of-service">服务条款</nuxt-link>
              </dd>
            </dl>
            <dl class={styles.block}>
              <dt>联系我们</dt>
              <dd>
                <div>电话咨询：400-088-8275</div>
              </dd>
            </dl>
          </div>
        </div>
        <div class={styles.copyright}>
          <div class={styles.wrapper}>
            <div class={styles.content}>
              <span>
                ©2014-2025{' '}
                <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank" rel="nofollow">
                  苏ICP备15042526号
                </a>
              </span>
              <span>版权所有 企查查科技股份有限公司</span>
              <span>
                增值电信业务经营许可证：
                <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank" rel="nofollow">
                  苏ICP证B2-20180251
                </a>
              </span>
              <span>企业征信备案号：04005</span>
              <span>
                <img src={IconPolice} width="20" height="20" alt="苏公网安备" />
              </span>
              <span>
                <a
                  href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=32059002002742"
                  target="_blank"
                  rel="nofollow"
                >
                  苏公网安备 32059002002742号
                </a>
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  },
});

export default Footer;
