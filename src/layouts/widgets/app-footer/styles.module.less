@import '@/assets/styles/token.less';

@color-text-black: rgba(255, 255, 255, 0.4);
@color-text-white: rgba(255, 255, 255, 1);

.container {
  background: #1a2334;
  color: @color-text-black;

  .wrapper {
    max-width: 1200px;
    margin: 0 auto;
  }

  .info {
    padding: 15px 0;
    display: flex;

    .block {
      margin: 0;

      &:not(:last-child) {
        margin-right: 100px;
      }

      dt {
        font-size: 14px;
        line-height: 22px;
        margin-bottom: 5px;
        color: @color-text-white;
      }

      dd {
        margin: 0;
        font-size: @base-text-xs;
        line-height: 18px;
        padding: 5px 0;
      }
    }

    a {
      color: @color-text-black;

      &:hover {
        color: @color-text-white;
      }
    }
  }

  .copyright {
    background-color: #111826;
    color: rgba(255, 255, 255, 0.4);

    .content {
      font-size: @base-text-xs;
      line-height: 18px;
      height: 48px;
      display: flex;
      align-items: center;
      // justify-content: center;
      text-align: left;
    }

    a {
      color: rgba(255, 255, 255, 0.4);

      &:hover {
        color: @base-color-white;
      }
    }

    span + span {
      margin-left: 10px;
    }
  }
}
