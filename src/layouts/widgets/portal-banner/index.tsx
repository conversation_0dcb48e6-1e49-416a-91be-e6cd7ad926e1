import { defineComponent, PropType } from 'vue';
import { isNumber } from 'lodash';
import styles from './styles.module.less';
import LandingWrapper from '@/features/landing-wrapper';
import env from '@/config/env';

const PortalBanner = defineComponent({
  functional: true,
  props: {
    user: {
      type: Object as PropType<{ [key in 'id' | 'userId' | 'name' | 'bundleError']: any } | undefined>,
      required: false,
    },
  },
  render(h, { props, listeners }) {
    const hasPermission = isNumber(props.user?.userId) && ![200008].includes(props.user?.bundleError?.code);

    const handleApply = () => {
      if (typeof listeners.apply === 'function') {
        listeners.apply();
      }
    };
    const handleIntro = () => {
      if (typeof listeners.intro === 'function') {
        listeners.intro();
      }
    };
    const handleEnter = () => {
      window.location.href = env.KYS_ENTRY;
    };
    return (
      <div class={styles.container}>
        <LandingWrapper>
          <h2 class={styles.title}>第三方风险排查系统</h2>
          <h3 class={styles.title}>风险早发现 信任零距离</h3>
          <p class={styles.description}>准入排查 | 风险预警 | 持续监控 | 利益冲突排查 | 企业画像归档</p>

          <div>
            <button onClick={handleIntro} class={[styles.button, styles.default]}>
              系统介绍视频
            </button>
            <button v-show={!hasPermission} onClick={handleApply} class={[styles.button, styles.primary]}>
              申请试用
            </button>
            <button v-show={hasPermission} onClick={handleEnter} class={[styles.button, styles.primary]}>
              进入产品
            </button>
          </div>
        </LandingWrapper>
      </div>
    );
  },
});
PortalBanner.emits = ['apply', 'intro'];

export default PortalBanner;
