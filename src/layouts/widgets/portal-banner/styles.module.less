@import '@/assets/styles/token.less';

.container {
  background: url('./assets/bg_banner.jpg') center center no-repeat #000906;
  background-size: cover;
  min-height: 552px;
  color: #fff;
  font-size: 14px;
  line-height: 22px;
  display: flex;
  flex-direction: column;
  justify-content: center;

  .title,
  .description {
    max-width: 640px;
  }

  .title {
    color: #fff;
    margin-bottom: 5px;
  }

  h2.title {
    font-size: 44px;
    line-height: 56px;
    margin-bottom: 20px;
  }

  h3.title {
    font-size: 18px;
    line-height: 26px;
  }

  .description {
    line-height: 22px;
    margin-bottom: 77px;
  }

  .button {
    min-width: 128px;
    line-height: 1;
    height: 40px;
    font-size: 16px;
    border: none;
    padding: 0 10px;
    outline: 0;
    transition: background-color 0.25s ease-in-out, border-color 0.25s ease-in-out;
    cursor: pointer;
    border-radius: 2px;
    border-width: 1px;
    border-style: solid;

    &.default {
      background: transparent;
      border-color: #fff;

      &:hover {
        background: @base-color-blue-500;
        border-color: @base-color-blue-500;
      }
    }

    &.primary {
      background: @base-color-blue-500;
      border-color: @base-color-blue-500;

      &:hover {
        background: #0069bf;
        border-color: #0069bf;
      }
    }
  }

  .button + .button {
    margin-left: 20px;
  }
}
