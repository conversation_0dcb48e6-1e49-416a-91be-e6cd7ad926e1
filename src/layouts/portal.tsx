import { computed, defineComponent, useContext } from '@nuxtjs/composition-api';
import { useToggle } from '@vueuse/core';
import Header from './widgets/app-header';
import Footer from './widgets/app-footer';
import Banner from './widgets/portal-banner';
import styles from './portal/styles.module.less';
import ContactSales from './widgets/contact-sales';
import IntroVideoModal from '@/features/intro-video-modal';
import ApplyTrialModal from '@/features/apply-trial-modal';
import ArrowRightIcon from '@/assets/icons/icon-wenzilianjiantou.svg?inline';
import { useTheme } from '@/composables/use-theme';
import { useTracer } from '@/composables/use-tracer';

const PortalLayout = defineComponent({
  name: 'PortalLayout',
  setup() {
    const { store, route } = useContext();

    const userProfile = computed(() => {
      return store.state.user.profile;
    });
    const [currentTheme] = useTheme('dark');
    const [introVideoVisible, setIntroVideoVisible] = useToggle(false);
    const [applyTrialVisible, setApplyTrialVisible] = useToggle(false);

    /**
     * 埋点追踪
     */
    const { clickEvent } = useTracer(route);

    /**
     * 点击系统介绍视频
     */
    const handleIntroVideo = () => {
      setIntroVideoVisible();
      clickEvent('系统介绍视频');
    }

    /**
     * 点击试用申请
     */
    const handleApplyTrial = () => {
      setApplyTrialVisible();
      clickEvent('申请试用');
    };

    /**
     * 申请试用提交成功
     */
    const handleApplyTrialSuccess = () => {
      clickEvent('申请试用-提交')
    }

    /**
     * 在线咨询
     */
    const handleContact = () => {
      window.open('/rover/udesk/chat');
      clickEvent('在线咨询');
    }

    return {
      currentTheme,
      userProfile,

      applyTrialVisible,
      handleApplyTrial,
      handleApplyTrialSuccess,

      introVideoVisible,
      handleIntroVideo,
      handleContact,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        <Header theme={this.currentTheme} user={this.userProfile} onApply={this.handleApplyTrial} />
        <Banner user={this.userProfile} onApply={this.handleApplyTrial} onIntro={this.handleIntroVideo} />
        <nuxt />
        <Footer />

        {/* 在线咨询 */}
        <ContactSales onContact={this.handleContact} />

        {/* 申请试用 */}
        <ApplyTrialModal v-model={this.applyTrialVisible} channel="第三方风险WEB-申请试用" onSuccess={this.handleApplyTrialSuccess}>
          <div class={styles.loginGuide} slot="extra">
            <em>已有账号，立即</em>
            <a href="/portal/login" rel="nofollow">
              <span>前往登录</span>
              <ArrowRightIcon />
            </a>
          </div>
        </ApplyTrialModal>
        {/* 视频介绍 */}
        <IntroVideoModal v-model={this.introVideoVisible} />
      </div>
    );
  },
});

export default PortalLayout;
