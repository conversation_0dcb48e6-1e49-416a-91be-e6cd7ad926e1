import { defineComponent, useContext } from '@nuxtjs/composition-api';
import { useToggle } from '@vueuse/core';
import AppHeader from './widgets/app-header';
import styles from './portal/styles.module.less';
import ApplyTrialModal from '@/features/apply-trial-modal';
import ArrowRightIcon from '@/assets/icons/icon-wenzilianjiantou.svg?inline';
import { useTracer } from '@/composables/use-tracer';

const AuthLayout = defineComponent({
  name: 'AuthLayout',
  setup() {
    const [applyTrialVisible, setApplyTrialVisible] = useToggle(false);
    const { route } = useContext();

    /**
     * 埋点追踪
     */
    const { clickEvent } = useTracer(route);

    /**
     * 点击试用申请
     */
    const handleApplyTrial = () => {
      setApplyTrialVisible();
      clickEvent('申请试用');
    };

    return {
      applyTrialVisible,
      handleApplyTrial,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        <AppHeader theme="light" onApply={this.handleApplyTrial} />

        <nuxt />

        {/* 申请试用 */}
        <ApplyTrialModal v-model={this.applyTrialVisible} channel="第三方风险WEB-申请试用">
          <div class={styles.loginGuide} slot="extra">
            <em>已有账号，立即</em>
            <a href="/portal/login" rel="nofollow">
              <span>前往登录</span>
              <ArrowRightIcon />
            </a>
          </div>
        </ApplyTrialModal>
      </div>
    );
  },
});

export default AuthLayout;
