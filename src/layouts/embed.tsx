import { defineComponent, useContext} from '@nuxtjs/composition-api'
import _ from 'lodash'
import LogoDefault from './widgets/app-header-pro/assets/logo-default.svg';
import LogoZeiss from './widgets/app-header-pro/assets/logo-zeiss.svg';
import * as AppHeader from "./widgets/app-header-pro";
import styles from "./embed/styles.module.less";
import env from "@/config/env";

const EmbedLayout = defineComponent({
  name: 'EmbedLayout',
  setup() {
    const { store } = useContext();

    const logoRenderer =() => {
      if (!store.getters['user/isLogin']) {
        return null
      }
      if (store.getters['user/isZeiss'] === 'v2') {
        return <img src={LogoZeiss} />;
      }
      return <img src={LogoDefault} />;
    }

    return {
      logoRenderer
    }
  },
  render() {
    return (
      <div class={styles.container}>
        <div class={styles.header}>
          <AppHeader.default>
            <AppHeader.Logo slot="logo" href={env.KYS_ENTRY}>
              {this.logoRenderer()}
            </AppHeader.Logo>
          </AppHeader.default>
        </div>
        <main class={styles.body}>
          <nuxt/>
        </main>
      </div>
    )
  }
})

export default EmbedLayout;
