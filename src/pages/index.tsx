import { defineComponent, onMounted, useContext } from '@nuxtjs/composition-api';
import { message } from 'ant-design-vue';
import { CASE_STUDY_LIST, LOGO_LIST, SCENARIO_LIST } from './portal/landing/config';
import ScenarioList from './portal/landing/widgets/scenario-list';
import Carousel from './portal/landing/widgets/carousel';
import LogoList from './portal/landing/widgets/logo-list';
import Section from '@/features/landing-section';
import Wrapper from '@/features/landing-wrapper';
import { useTracer } from '@/composables/use-tracer';

const LandingPage = defineComponent({
  name: 'LandingPage',
  layout: 'portal',
  setup() {
    const scenarioList = SCENARIO_LIST;
    const caseStudyList = CASE_STUDY_LIST;
    const logoList = LOGO_LIST;

    const { route } = useContext();
    const { clickEvent } = useTracer(route);

    const handleScrollToSection = (targetId: string, targetName: string) => {
      const target = document.getElementById(targetId);
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
        });
      }
      clickEvent(targetName);
    };

    onMounted(() => {
      const errorMessage = route.value.query?.errorMessage;
      if (errorMessage && typeof errorMessage === 'string') {
        message.warning(errorMessage);
      }
    });
    return {
      scenarioList,
      caseStudyList,
      handleScrollToSection,
      logoList,
    };
  },
  render() {
    const { scenarioList, logoList, caseStudyList } = this;
    return (
      <div>
        <Section
          title="企查查第三方风险排查系统 · 多场景赋能"
          description="助力企业搭建合作伙伴全方位风险管理体系，降低企业风险管理成本，帮助企业最大限度规避防范财务、法律和声誉风险，将合作方风险控制在最前端。"
          theme="default"
        >
          <Wrapper>
            <ScenarioList items={scenarioList} onClick={this.handleScrollToSection} />
          </Wrapper>
        </Section>

        <Section title="供应商风险管理" theme="blue" id="supplier-risk-management">
          <Wrapper>
            <Carousel items={caseStudyList[0]} />
          </Wrapper>
        </Section>

        <Section title="采购围串标管理" theme="gray" id="purchase-contract-risk-management">
          <Wrapper>
            <Carousel items={caseStudyList[1]} />
          </Wrapper>
        </Section>

        <Section title="企业合规内控管理" theme="blue" id="supplier-compliance-risk-management">
          <Wrapper>
            <Carousel items={caseStudyList[2]} />
          </Wrapper>
        </Section>

        <Section
          title="合规筛查 · 特定利益关系排查"
          theme="gray"
          id="supplier-compliance-specific-risk-assessment"
        >
          <Wrapper>
            <Carousel items={caseStudyList[3]} />
          </Wrapper>
        </Section>

        <Section
          title="客户案例"
          description="实现合作伙伴风险统筹管理，通过对合作伙伴信息库、合作准入、利益冲突排查、招标排查、风险监控分析，为企业合作伙伴管理提供关键决策与风险管控依据"
        >
          <Wrapper style={{ width: '930px' }}>
            <LogoList logos={logoList} />
          </Wrapper>
        </Section>
      </div>
    );
  },
});

export default LandingPage;
