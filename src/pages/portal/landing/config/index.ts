import Scenario1 from '../assets/scenarios/scenario-1.png';
import Scenario2 from '../assets/scenarios/scenario-2.png';
import Scenario3 from '../assets/scenarios/scenario-3.png';
import Scenario4 from '../assets/scenarios/scenario-4.png';

import CaseStudy11 from '../assets/case-study/case-study-1-1.jpg';
import CaseStudy12 from '../assets/case-study/case-study-1-2.jpg';
import CaseStudy13 from '../assets/case-study/case-study-1-3.jpg';
import CaseStudy14 from '../assets/case-study/case-study-1-4.jpg';
import CaseStudy15 from '../assets/case-study/case-study-1-5.jpg';
import CaseStudy21 from '../assets/case-study/case-study-2-1.jpg';
import CaseStudy22 from '../assets/case-study/case-study-2-2.jpg';
import CaseStudy23 from '../assets/case-study/case-study-2-3.jpg';
import CaseStudy24 from '../assets/case-study/case-study-2-4.jpg';
import CaseStudy31 from '../assets/case-study/case-study-3-1.jpg';
import CaseStudy32 from '../assets/case-study/case-study-3-2.jpg';
import CaseStudy33 from '../assets/case-study/case-study-3-3.jpg';
import CaseStudy41 from '../assets/case-study/case-study-4-1.jpg';

/**
 * 三大场景
 */
export const SCENARIO_LIST = Object.freeze([
  {
    key: 'supplier-risk-management',
    name: '供应商准入风险管理、巡检审查',
    description: '准入风险排查、交叉重叠排查、黑名单排查、供应商巡检、持续排查监控',
    icon: Scenario1,
  },
  {
    key: 'purchase-contract-risk-management',
    name: '采购招标围串标风险管理',
    description: '投资任职关系发现、疑似关系挖掘、历史投标分析、历史围串标不良记录排查',
    icon: Scenario2,
  },
  {
    key: 'supplier-compliance-risk-management',
    name: '第三方合规内控风险管理',
    description: '合作准入排查、持续风险监控、利益冲突排查、深度关系挖掘',
    icon: Scenario3,
  },
  {
    key: 'supplier-compliance-specific-risk-assessment',
    name: '合规筛查·特定利益关系排查',
    description: '审查交易对方、特定利益关系识别',
    icon: Scenario4,
  },
]);

/**
 * 使用场景
 */
export const CASE_STUDY_LIST = Object.freeze([
  [
    {
      name: '准入风险排查',
      icon: 'icon-zhunrufengxianpaicha',
      sub: [
        {
          title: '潜在风险识别：',
          content:
            '在供应商准入环节，我们进行全面的资质综合排查，以高效快速地识别潜在的风险，如行贿犯罪、商业贿赂、内部腐败、潜在利益冲突等。这有助于排除存在明显问题的企业，从源头预防潜在风险的发生。',
        },
        {
          title: '查查信用分：',
          content:
            '通过机器学习算法及大数据模型，从企业背景、经营状况、资质评价、履约历史、管理水平等多维度给企业信用定量打分，辅助风险评估。',
        },
      ],
      decorator: CaseStudy11,
    },
    {
      name: '交叉重叠排查',
      icon: 'icon-jiaochazhongdiepaicha',
      description:
        '针对现有及拟准入供应商清单，识别检验隐藏的所有权结构，交叉持股、交叉任职以及其他可能存在的潜在风险。',
      decorator: CaseStudy12,
    },
    {
      name: '黑名单排查',
      icon: 'icon-heimingdanguanli',
      description:
        '将不良合作企业纳入内部黑名单，系统自动关联排查供应商与内部黑名单的持股、任职关系，规避黑名单企业通过改头换面重新参与公司产品供应。同时辅以外部黑名单数据如不良采购供应商等排查其外部风险，以确保优质供应商准入。',
      decorator: CaseStudy13,
    },
    {
      name: '供应商巡检',
      icon: 'icon-zhunrupaicha',
      description:
        '对存量供应商采取定期巡检的方式，进行信息变更核查、风险盘点，定期自动获取、核查、比对供应商的资质、风险信息。',
      decorator: CaseStudy14,
    },
    {
      name: '持续排查监控',
      icon: 'icon-chixujintiao2',
      description:
        '新进供应商在进行注册、准入、合同签约等环节，进入正式合作中时，需要对业务合作中的风险持续进行监控管理，避免因供应商突发负面风险信号，企业未能及时捕捉或感知滞后，导致供应中断、财务损失等风险事件。',
      decorator: CaseStudy15,
    },
  ],
  // 采购转串标
  [
    {
      name: '投资任职关系发现',
      icon: 'icon-touzirenzhiguanxifaxian',
      description:
        '对投标候选供应商的内外部多种关联关系进行合规排查，排查投标供应商之间的交叉持股、董监高相互任职、同集团企业等关系，避免一家独大。',
      decorator: CaseStudy21,
    },
    {
      name: '疑似关系挖掘',
      icon: 'icon-yisiguanxiwajue',
      description:
        '通过对投标供应商之间存在的疑似相同电话、相同邮箱、相同知识产权、相同案件信息进行分析，深度挖掘企业间存在的潜在关联关系。',
      decorator: CaseStudy22,
    },
    {
      name: '历史投标分析',
      icon: 'icon-lishitoubiaofenxi',
      description: '通过目标企业共同投标记录，各自中标率，共同投标人等数据，分析历史投标情况。',
      decorator: CaseStudy23,
    },
    {
      name: '围串标不良记录排查',
      icon: 'icon-lishiweichuanbiaobuliangjilu',
      description: '根据司法判决、行政处罚、央企公示信息等数据，获取历史围串标不良记录，并提示对应风险。',
      decorator: CaseStudy24,
    },
  ],
  // 企业合规内控管理
  [
    {
      name: '合作准入排查',
      icon: 'icon-hezuozhunrupaicha',
      description:
        '对第三方基本信息、法律诉讼、行政监管、经营稳定性等多维度风险事项进行排查，结合与合作伙伴、内部员工、内外黑名单数据等多类主体间的关联关系排查，有效降低企业第三方合作伙伴可能存在的行贿犯罪、商业贿赂、内部腐败、潜在利益冲突等风险，帮助企业排除有明显问题的合作方，防范风险于未然。',
      decorator: CaseStudy31,
    },
    {
      name: '利益冲突排查',
      icon: 'icon-liyichongtupaicha',
      description:
        '对企业内部高管、员工、员工近亲属、曾被处罚的现任员工或前员工与第三方企业进行任职、对外投资排查，识别员工是否在相关供应商担任合作伙伴的股东及董监高法等，规避相关利益冲突风险。',
      decorator: CaseStudy32,
    },
    {
      name: '深度关系挖掘',
      icon: 'icon-shenduguanxiwajue',
      description:
        '深度挖掘内部股东高管与已准入或合作中供应商之间的深层次关系网络，避免商业贿赂、利益输送等风险。',
      decorator: CaseStudy33,
    },
  ],
  // 合规筛查 · 特定利益关系排查
  [
    {
      name: '特定利益关系排查',
      icon: 'icon-hezuozhunrupaicha',
      description:
        '特定利益关系排查采用先进的大数据处理技术，挖掘持定利益关系上下游之间的交叉持股、控制关系、相同人员以及相同经营地址、相同联系方式、相同担保关联等关联关系，识别潜在虚假贸易的可能性，协助企业高效防控虚假贸易业务。',
      decorator: CaseStudy41,
    },
  ],
]);

export const LOGO_LIST = Object.freeze<string[]>([
  require('../assets/logo/logo-1-1.jpg'),
  require('../assets/logo/logo-1-2.jpg'),
  require('../assets/logo/logo-1-3.jpg'),
  require('../assets/logo/logo-1-4.jpg'),
  require('../assets/logo/logo-1-5.png'),
  require('../assets/logo/logo-1-6.png'),

  require('../assets/logo/logo-2-1.png'),
  require('../assets/logo/logo-2-2.png'),
  require('../assets/logo/logo-2-3.png'),
  require('../assets/logo/logo-2-4.png'),
  require('../assets/logo/logo-2-5.png'),
  require('../assets/logo/logo-2-6.png'),
]);
