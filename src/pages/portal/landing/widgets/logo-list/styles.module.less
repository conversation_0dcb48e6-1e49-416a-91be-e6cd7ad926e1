.container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between;
  margin: -12px 0;
  &.default {
    .item {
      margin: 12px 0;
      width: 140px;
      height: 80px;
    }
  }
  &.large {
    .item {
      margin: 12px 0;
      width: 180px;
      height: 80px;
    }
  }
  &.compact {
    width: 930px;
    justify-content: center;
    margin: 0 -7.5px;
    margin: 0 auto;
    .item {
      margin: 12px 7.5px;
      width: 140px;
      height: 80px;
    }
  }
  .item {
    background: #fff;
    box-shadow: 0px 1px 10px 0px #ebeef6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 10px;
    img {
      max-width: 100%;
    }
  }
}
