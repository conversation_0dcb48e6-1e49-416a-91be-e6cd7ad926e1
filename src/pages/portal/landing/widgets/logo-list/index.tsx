import { defineComponent, PropType } from 'vue';
import styles from './styles.module.less';

const LogoList = defineComponent({
  functional: true,
  props: {
    logos: {
      type: Array as PropType<string[] | readonly string[]>,
      required: true,
    },
    logoSize: {
      type: String,
      default: 'default',
    },
  },
  render(h, { props }) {
    const { logos } = props;
    return (
      <div class={[styles.container, styles[props.logoSize]]}>
        {logos.map((logoPath, index) => {
          return (
            <div class={styles.item} key={index}>
              <img src={logoPath} />
            </div>
          );
        })}
      </div>
    );
  }
});

export default LogoList;
