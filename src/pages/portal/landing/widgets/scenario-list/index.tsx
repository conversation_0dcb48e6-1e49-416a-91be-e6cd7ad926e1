import { defineComponent, PropType } from 'vue';
import styles from './styles.module.less';

type ScenarioListItem = {
  name: string;
  description: string;
  icon: string;
};

const ScenarioList = defineComponent({
  functional: true,
  props: {
    items: {
      type: Array as PropType<Array<ScenarioListItem> | ReadonlyArray<ScenarioListItem>>,
      required: true,
    },
  },
  render(h, { props, listeners }) {
    return (
      <div class={styles.container}>
        {props.items.map(({ key, name, description, icon }) => {
          return (
            <div
              class={styles.item}
              key={key}
              onClick={() => {
                if (listeners.click && typeof listeners.click === 'function') {
                  listeners.click?.(key, name);
                }
              }}
            >
              <div class={styles.icon}>
                <img src={icon} width="45" height="45" />
              </div>
              <h4 class={styles.title}>{name}</h4>
              <p class={styles.description}>{description}</p>
            </div>
          );
        })}
      </div>
    );
  },
});

export default ScenarioList;
