@import '@/assets/styles/token.less';

.container {
  .tabs {
    display: flex;
    align-items: center;
    justify-content: center;

    .item {
      position: relative;
      padding-bottom: 20px;

      &::after {
        content: '';
        visibility: hidden;
        display: block;
        width: 0;
        height: 0;
        bottom: 0;
        border-left: 13px solid transparent;
        border-right: 13px solid transparent;
        border-bottom: 10px solid #fff;
        position: absolute;
        left: 50%;
        margin-left: 26px / 2 * -1;
      }

      & + .item {
        margin-left: 20px;
      }

      &.active {
        &::after {
          visibility: visible;
        }

        .tab {
          background: @base-color-blue-500;
          color: #fff;
          cursor: default;

          .icon {
            color: @base-color-blue-500;
            background: @base-color-white;
          }
        }
      }
    }
  }

  .tab {
    background: #fff;
    border-radius: 6px;
    color: @base-color-black-600;
    min-width: 224px;
    height: 54px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    .icon {
      margin-right: 15px;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background: linear-gradient(49deg, #a0d4ff 15%, #b6deff 15%, #4097ff 86%);
      display: inline-flex;
      align-items: center;
      justify-content: center;
      color: @base-color-white;

      > i {
        font-size: 16px;
      }
    }

    .title {
      font-size: 18px;
      line-height: 26px;
      font-weight: 700;
    }
  }

  .content {
    background: #fff;
    border-radius: 4px;
    padding: 20px 30px;
    display: flex;

    .main {
      flex: 1;
      padding-right: 40px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;

      .title {
        font-size: 26px;
        font-weight: 700;
        line-height: 32px;
        color: @base-color-black-800;
        margin-bottom: 15px;
      }

      .description {
        font-size: 16px;
        line-height: 26px;
        color: @base-color-black-400;
      }

      .sub:not(:last-child) {
        margin-bottom: 10px;
      }

      .emphasis {
        font-weight: 500;
        color: @base-color-black-600;
      }
    }
  }
}
