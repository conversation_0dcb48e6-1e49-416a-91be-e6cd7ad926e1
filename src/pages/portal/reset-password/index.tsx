import { defineComponent, ref, unref, useRouter } from '@nuxtjs/composition-api';
import type { WrappedFormUtils } from 'ant-design-vue/types/form/form';
import { Modal, message as Message, Button, ConfigProvider } from 'ant-design-vue';
import axios from 'axios';
import Form from './form';
import styles from './styles.module.less';
import { service } from '@/services';
import ArrowRightIcon from '@/assets/icons/icon-wenzilianjiantou.svg?inline';
import { emailReg } from '@/utils/pattern';

enum SubmitStatus {
  Idle = 'idle',
  Pending = 'pending',
  Success = 'success',
  Error = 'error',
}

const ResetPasswordPage = defineComponent({
  name: 'ResetPasswordPage',
  layout: 'reset-password',
  props: {},
  setup() {
    const submitStatus = ref<SubmitStatus>(SubmitStatus.Idle);
    const formRef = ref<WrappedFormUtils>();
    const router = useRouter();

    /**
     * 表单验证
     * @param formError
     * @param formData
     */
    const formValidator = async (formError, formData) => {
      if (formError) {
        Message.error('有必填项还未填写！');
        return;
      }
      try {
        submitStatus.value = SubmitStatus.Pending;
        const key = emailReg.test(formData.phone) ? 'email' : 'phone';
        const { data } = await service.user.resetPassword({
          [key]: formData.phone,
          newPassword: formData.password,
          verifyCode: formData.code,
          confirmPassword: formData.confirmPassword,
        });
        if (data?.status === 200) {
          submitStatus.value = SubmitStatus.Success;
          // 成功提示消息
          Modal.success({
            title: '密码修改成功！请使用新密码重新登录',
            okText: '知道了',
            onOk() {
              router.push('/portal/login');
            },
          });
        } else {
          throw new Error('操作出错，请稍后再试');
        }
      } catch (error) {
        if (axios.isAxiosError(error)) {
          // 错误消息
          Message.error(error?.response?.data?.error ?? '操作出错，请稍后再试');
        } else {
          Message.warning(error?.message ?? '操作出错，请稍后再试');
        }
        submitStatus.value = SubmitStatus.Error;
        (formRef.value as any)?.$children?.[0]?.$refs?.noCaptchaRef?.reset();
      }
    };

    /**
     * 提交表单
     */
    const handleFormSubmit = () => {
      const formRefRaw = unref(formRef);
      formRefRaw?.validateFields(formValidator);
    };

    return {
      formRef,
      submitStatus,
      handleFormSubmit,
    };
  },
  render() {
    return (
      <ConfigProvider autoInsertSpaceInButton={false}>
        <div class={styles.container}>
          <div class={[styles.block, styles.form]}>
            <div class={styles.header}>
              <div class={styles.title}>重置密码</div>
              <div class={styles.desc}>
                <nuxt-link class={styles.link} to="/portal/login" rel="nofollow">
                  <span>返回登录</span>
                  <ArrowRightIcon />
                </nuxt-link>
              </div>
            </div>

            <div class={styles.body}>
              <Form ref="formRef" />
            </div>

            <div class={styles.footer}>
              <Button
                onClick={this.handleFormSubmit}
                loading={this.submitStatus === SubmitStatus.Pending}
                type="primary"
                size="large"
                block
              >
                确定
              </Button>
            </div>
          </div>
        </div>
      </ConfigProvider>
    );
  },
});

export default ResetPasswordPage;
