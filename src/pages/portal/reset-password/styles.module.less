.container {
  padding-top: 52px;
  position: relative;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    top: 52px;
    background: url('./assets/bg-reset-password.jpg') no-repeat 50% 50% / cover;
    height: 400px;
  }

  .block {
    position: relative;
    background: #fff;
    padding: 30px;
    border-radius: 4px;
    box-shadow: 0px 2px 15px 0px rgba(0,0,0,.1);

    .header {
      display: flex;
      justify-content: space-between;
      line-height: 30px;
      margin-bottom: 15px;

      .title {
        color: #303133;
        font-size: 18px;
        font-weight: 700;
        line-height: 30px;
      }

      .link {
        display: flex;
        align-items: center;
      }
    }

    .footer {
      margin-top: 15px;
    }
  }

  .form {
    :global {
      .ant-row {
        margin-bottom: 15px;
  
        &:last-child {
          margin-bottom: 0;
        }
      }
  
      .ant-form-item-label {
        text-align: left;
      }
  
      .ant-form-item-control {
        line-height: 40px;
      }
  
      .ant-form-item-required::before {
        position: static;
      }
  
      // 错误提示
      .ant-form-explain {
        margin-top: 10px;
      }
  
      // 帮助提示: extra
      .ant-form-extra {
        margin-top: 10px;
        padding: 0;
        font-size: 13px;
        line-height: 20px;
        color: #999;
      }
  
      // 输入框右方按钮：获取验证码
      .ant-input-group-addon {
        background: #fff;
      }
  
      // 密码隐藏图标
      .ant-input-password-icon {
        &:hover {
          color: #128bed;
        }
      }
    }
  }
}