import { defineComponent, ref, shallowReactive } from 'vue';
import { Tooltip } from 'ant-design-vue';
import styles from './with-login.module.less';
import env from '@/config/env';

const OAuth = defineComponent({
  name: 'OAuth',
  setup() {
    const visible = ref(false);
    const showLinks = () => {
      visible.value = true;
    };
    const redirectURL = `${env.KYS_HOME}${env.KYS_ENTRY}`;

    const links = shallowReactive({
      sso: `${env.ENTERPRISE_HOME}/sso-login?back=${encodeURIComponent(redirectURL)}&fallbackUrl=${
        env.KYS_HOME
      }`,
      zeiss: `${env.KYS_HOME}/qcc/e/login/custom/zeiss?redirect=${encodeURIComponent(redirectURL)}&fallbackUrl=${
        env.KYS_HOME
      }/error/auth/zeiss`, // 蔡司专属错误提示页
    });
    return {
      visible,
      showLinks,
      links,
    };
  },
  render() {
    return (
      <div class={styles.oauth}>
        <a v-show={!this.visible} onClick={this.showLinks}>
          其他方式登录
        </a>
        {/* <Tooltip title="微信" placement="bottom">
          <a v-show={this.visible} class={[styles.link, styles.icon]} href={this.links.sso}>
            <i class={[styles.brand, styles.wechat]} />
          </a>
        </Tooltip> */}
        {/* <Tooltip title="企业微信" placement="bottom">
          <a v-show={this.visible} class={[styles.link, styles.icon]} href={this.links.sso}>
            <i class={[styles.brand, styles.echat]} />
          </a>
        </Tooltip> */}
        <Tooltip title="单点登录SSO" placement="bottom">
          <a v-show={this.visible} class={[styles.link, styles.icon]} href={this.links.sso}>
            <i class={[styles.brand, styles.sso]} />
          </a>
        </Tooltip>
        <Tooltip title="卡尔蔡司专属账号" placement="bottom">
          <a v-show={this.visible} class={[styles.link, styles.icon]} href={this.links.zeiss}>
            <i class={[styles.brand, styles.zeiss]} />
          </a>
        </Tooltip>
      </div>
    );
  },
});

export default OAuth;
