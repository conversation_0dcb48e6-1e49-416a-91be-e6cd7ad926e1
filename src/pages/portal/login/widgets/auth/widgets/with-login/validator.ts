import { message as Message } from "ant-design-vue";

// 登录后跳转目标
const validator = (pattern: RegExp, errorMessage: string) => (value: string) => {
  const result = pattern.test(value.trim());
  if (!result) {
    Message.warn(errorMessage);
  }
  return result;
};

export const phoneNumberValidator = (value: string) => {
  if (!value) {
    Message.warn({
      key: 'auth-feedback',
      content: '请输入手机号',
    });
    return false;
  }
  return validator(/^1[3-9]\d{9}$/, '请输入正确的手机号')(value);
};

export const verifyCodeValidator = (value: string): boolean => {
  if (!value) {
    Message.warn({
      key: 'auth-feedback',
      content: '请输入验证码',
    });
    return false;
  }
  return validator(/\d{6}/, '验证码不正确，请重新输入')(value);
};

export const emailValidator = (value: string): boolean => {
  if (!value) {
    Message.warn({
      key: 'auth-feedback',
      content: '请输入电子邮箱',
    });
    return false;
  }
  return validator(/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/, '电子邮箱格式不正确，请重新输入')(value);
};

export const passwordValidator = (value: string): boolean => {
  if (!value) {
    Message.warn({
      key: 'auth-feedback',
      content: '请输入密码',
    });
    return false;
  }
  return true;
};

