@border-radius-base: 2px;
@color-primary: #128bed;
@line-height: 24px;

.container {
  width: 400px;
  height: 442px;
  padding: 20px 0 25px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-size: 14px;

  .body {
    flex: 1;
  }

  .tabs {
    margin-top: 30px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;

    .item {
      color: #666;
      cursor: pointer;
      font-size: 16px;
      line-height: @line-height;
      position: relative;
      padding-bottom: 8px;

      &:hover {
        color: @color-primary;
      }

      &:not(:last-child) {
        margin-right: 30px;
      }

      &::after {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 2px;
        opacity: 0;
        background: @color-primary;
        // transition: opacity 0.5s ease;
      }

      &.active {
        color: @color-primary;
        font-weight: 700;

        &::after {
          opacity: 1;
        }
      }
    }
  }

  .info {
    display: flex;
    justify-content: space-between;
  }

  .row {
    padding: 10px 30px;
  }

  .actions {
    line-height: @line-height;
    display: flex;
    justify-content: space-between;

    a {
      color: #999;

      &:hover {
        color: @color-primary;
      }
    }
  }

  .footer {
    color: #999;
    font-size: 12px;
    line-height: 18px;
    text-align: center;

    p:not(:last-child) {
      margin-bottom: 5px;
    }

    :global {
      .ant-checkbox-input {
        vertical-align: text-bottom;
        margin-right: 4px;
      }
    }
  }
}

.input {
  :global {
    .ant-input {
      font-size: 14px;
      border-color: #d8d8d8;
      border-radius: @border-radius-base;

      &:hover,
      &:focus {
        border-color: @color-primary;
      }

      &:focus {
        box-shadow: none;
      }
    }
  }

  .action {
    cursor: pointer;
  }
  // 获取验证码控件
  &.verify-code {
    :global {
      .ant-input-suffix {
        height: 100%;
        right: 0;
      }

      .ant-input {
        padding-right: 105px !important;
      }
    }

    .action {
      line-height: 1;
      // padding: 0 15px;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      width: 109px;

      &::after {
        content: '';
        position: absolute;
        top: 1px;
        bottom: 1px;
        left: 0;
        width: 1px;
        background: #d8d8d8;
      }

      &.disable {
        color: @color-primary;
        cursor: default;
      }
    }
  }

  &.password {
    .action {
      font-size: 16px;

      &.active {
        color: @color-primary;
      }
    }
  }
}

.oauth {
  display: flex;
  align-items: center;
  // justify-content: space-between;
  .link {
    display: inline-block;
    line-height: @line-height;

    &:not(:first-child) {
      margin-left: 10px;
    }
  }

  .brand {
    width: @line-height;
    height: @line-height;
    background: no-repeat 50% 50%;
    background-size: @line-height;
    display: block;

    &.zeiss {
      background-image: url('../../assets/icon-zeiss.png');
    }

    &.sso {
      background-image: url('../../assets/icon-sso-fade.png');

      &:hover {
        background-image: url('../../assets/icon-sso.png');
      }
    }

    &.echat {
      background-image: url('../../assets/icon-echat-fade.png');

      &:hover {
        background-image: url('../../assets/icon-echat.png');
      }
    }

    &.wechat {
      background-image: url('../../assets/icon-wechat-fade.png');

      &:hover {
        background-image: url('../../assets/icon-wechat.png');
      }
    }
  }
}
