@border-radius-base: 2px;
@color-primary: #128bed;
@color-primary-hover: #0d61a6;

.container {
  position: relative;
  background: #fff;
  overflow: hidden;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);

  a {
    color: @color-primary;

    &:hover {
      color: @color-primary-hover;
    }
  }

  .loading {
    height: 100%;

    :global(.ant-spin-container) {
      height: 100%;
    }
  }

  .switch {
    display: none; // NOTE: 暂不支持扫码登录，隐藏切换按钮
    cursor: pointer;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
    width: 60px;
    height: 60px;
    background: no-repeat center;
    background-size: 60px 60px;

    &.login {
      background-image: url('./assets/icon-scan-mode.png');
    }

    &.scan {
      background-image: url('./assets/icon-login-mode.png');
    }
  }
}
