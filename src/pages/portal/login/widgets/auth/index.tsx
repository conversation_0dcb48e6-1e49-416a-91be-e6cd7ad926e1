import { Spin } from 'ant-design-vue';
import { computed, defineComponent, ref, watchPostEffect , useContext } from '@nuxtjs/composition-api';
import WithLogin from './widgets/with-login';
// import WithScan from './widgets/with-scan';
import styles from './auth.module.less';

const AuthModule = defineComponent({
  name: 'AuthModule',
  setup() {
    const mode = ref<'account' | 'scan'>('account');
    const handleChangeMode = () => {
      mode.value = mode.value === 'account' ? 'scan' : 'account';
    };

    // 判断是否已登录：如果已登录跳转到应用页面
    const { store } = useContext();
    const userProfile = computed(() => {
      return store.state.user.profile;
    });

    const isLoading = ref(true);
    watchPostEffect(() => {
      if (userProfile.value === null) {
        isLoading.value = false;
      } else {
        window.location.href = '/app/';
      }
    });

    return {
      mode,
      handleChangeMode,
      isLoading,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        {/* <div
          onClick={this.handleChangeMode}
          class={{
            [styles.switch]: true,
            [styles[this.mode]]: true,
          }}
        /> */}
        <Spin spinning={this.isLoading} wrapperClassName={styles.loading}>
          <WithLogin v-show={this.mode === 'account'} />
          {/* <WithScan v-show={this.mode === 'scan'} /> */}
        </Spin>
      </div>
    );
  },
});

export default AuthModule;
