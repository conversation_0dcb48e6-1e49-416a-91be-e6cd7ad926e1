import { defineComponent } from '@nuxtjs/composition-api';
import Auth from './widgets/auth';
import Wrapper from './widgets/wrapper';
import styles from './login.module.less';

const LoginPage = defineComponent({
  name: 'LoginPage',
  layout: 'auth',
  render() {
    return (
      <div class={styles.container}>
        <Wrapper>
          <div class={styles.hero}>
            <div class={styles.info}>
              <h1>第三方风险排查系统</h1>
              <h2>风险早发现 信任零距离</h2>
              <p>
                企查查助力企业搭建合作伙伴全方位风险管理体系，降低企业风险管理成本，提供第三方风险管理综合解决方案，帮助企业最大限度规避、防范财务、法律和声誉风险。
              </p>
            </div>
            <Auth />
          </div>
        </Wrapper>
      </div>
    );
  },
});

export default LoginPage;
