@fullHeight: calc(100vh - 52px - 20px);

.content {
  overflow-y: auto;
  border-radius: 4px;
  height: @fullHeight;
  background-color: #fff;
  padding: 15px;
}

.loading {
  text-align: center;
}

.sidebar {
  height: calc(100vh - 52px);
  border-right: 1px solid #eee;
  background-color: #fff;

  :global {
    .ant-menu {
      border-right: 0;
      color: #333;

      .ant-menu-item {
        margin-top: 0;
        margin-bottom: 0;

        &:hover {
          background: #f2f8fe;
        }
      }
    }
  }
}

.sidebarHeader {
  height: 78px;
  line-height: 78px;
  padding: 0 20px;
  background: url('../assets/icon-release-log-sider-header.png') no-repeat;
  background-size: 100%;
  font-size: 20px;
  font-weight: bold;
}

.sidebarExtra {
  padding: 10px 15px;
  font-size: 12px;
  color: #999;
  position: relative;

  &::before {
    content: '';
    height: 1px;
    background: #eee;
    position: absolute;
    left: 15px;
    right: 15px;
    top: 0;
  }
}

.menus {
  width: 250px;
  max-height: calc(@fullHeight - 80px);
  overflow-y: auto;

  :global {
    .ant-menu-item {
      margin-top: 0;
      margin-bottom: 0;
    }

    .ant-menu-item-selected {
      border-right: 3px solid #128bed;
    }
  }
}

.articleHeader {
  padding: 15px 0;
  border-bottom: 1px solid #f7f7f7;
  position: sticky;
  top: 0;
  background-color: #fff;
}

.articleTitle {
  font-size: 24px;
  line-height: 32px;
  color: #333;
  font-weight: bold;
}

.articleTime {
  margin-top: 10px;
  color: #999;
}

.articleContent {
  padding: 15px 0 !important;
  height: auto !important;

  img {
    width: 100%;
  }
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
