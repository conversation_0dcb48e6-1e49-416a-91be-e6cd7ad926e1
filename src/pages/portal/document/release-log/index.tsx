import { computed, defineComponent, onMounted, ref } from '@nuxtjs/composition-api';
import { Menu, Row, Col, Empty, Spin } from 'ant-design-vue';
import moment from 'moment';
import styles from './release-log.module.less';
import { service } from '@/services';
import './assets/quill.snow.css';
import {useFetchState} from "@/utils/use-fetch-state";

const ReleaseLog = defineComponent({
  name: 'ReleaseLog',
  layout: 'embed',
  middleware: 'auth',
  setup() {
    const selectedKeys = ref<any[]>([]);
    const fetchData = async () => {
      const res = await service.updateLog.list();
      if (res.data?.[0]?.logId) {
        selectedKeys.value = [res.data?.[0]?.logId];
      }
      return res;
    };
    const { isLoading, execute, result } = useFetchState(fetchData);
    const data = computed(() => result.value?.data);

    const activeItem = computed(() => {
      const id = selectedKeys.value[0];
      return data.value?.find((item) => item.logId === id);
    });

    onMounted(() => {
      execute();
    });
    return {
      isLoading,
      execute,
      result,
      selectedKeys,
      activeItem,
      data,
    };
  },
  head: {
    title: '更新日志',
    titleTemplate: '%s - 第三方风险排查系统',
  },

  render() {
    if (this.isLoading) {
      return (
        <div style={{ height: 'calc(100vh - 72px)' }} class="flex flex-col items-center justify-center">
          <Spin spinning={true} />
        </div>
      );
    }
    if (!this?.data?.length) {
      return (
        <Empty
          style={{ height: 'calc(100vh - 72px)' }}
          class={['ant-empty-small', styles.empty]}
        >
          <div slot="description">
            <div>暂无数据</div>
          </div>
        </Empty>
      );
    }

    return (
      <div class={styles.container}>
        <Row type="flex">
          <Col class={styles.sidebar}>
            <div class={styles.sidebarHeader}>更新日志</div>
            <Menu class={styles.menus} v-model={this.selectedKeys}>
              {this?.data.map(({ logId, name }) => (
                <Menu.Item key={logId}>{name}</Menu.Item>
              ))}
            </Menu>
            <div class={styles.sidebarExtra}>
              <span>为您展示近三个月的更新日志</span>
            </div>
          </Col>
          <Col flex="1" class="" style="overflow: hidden;padding:10px;">
            <div class={styles.content}>
              <div class={styles.articleHeader}>
                <div class={styles.articleTitle}>{this.activeItem.name}</div>
                <div class={styles.articleTime}>
                  更新时间：{moment(this.activeItem.releaseTime).format('YYYY-MM-DD HH:mm:ss')}
                </div>
              </div>
              <div
                class={[styles.articleContent, ' ql-snow ql-editor']}
                domPropsInnerHTML={this.activeItem.content}
              ></div>
            </div>
          </Col>
        </Row>
      </div>
    );
  },
});

export default ReleaseLog;
