import { defineComponent, computed, useContext, useAsync, ref, onMounted } from '@nuxtjs/composition-api';
import { Breadcrumb, Input, Menu } from 'ant-design-vue';
import moment from 'moment';
import Fuse from 'fuse.js';
import styles from './guide-manual.module.less';
import { MENU_CONFIG, FLAT_MENU_CONFIG } from './config/guide-manual.config';
import PreNextArticle from './widgets/pre-next-article';
import IconArrow from '@/assets/icons/icon-wenzilianjiantou.svg?inline';
import DocSearch from './widgets/doc-search';

const GuideManual = defineComponent({
  name: 'GuideManual',
  layout: 'embed',
  // middleware: 'auth',
  setup() {
    const { $content, error, params } = useContext();

    const selectedMenu = computed(() => [params.value.slug]);



    const selectedMenuItem = computed(
      () => FLAT_MENU_CONFIG.find((menu) => selectedMenu.value.includes(menu.key)) || {}
    );

    const openKeys = ref(
      selectedMenuItem.value ? [selectedMenuItem.value.parent?.key || selectedMenuItem.value.key] : []
    );

    const isRootSelected = (key) => {
      return openKeys.value.includes(params.value.slug) && params.value.slug === key;
    };

    const prevNextSelected = computed(() => {
      if (selectedMenuItem.value.isRoot) return null;
      const index = FLAT_MENU_CONFIG.findIndex((item) => item.key === params.value.slug);
      return {
        prev: FLAT_MENU_CONFIG[index - 1]?.isRoot ? null : FLAT_MENU_CONFIG[index - 1],
        next: FLAT_MENU_CONFIG[index + 1]?.isRoot ? null : FLAT_MENU_CONFIG[index + 1],
      };
    });

    const pageData = useAsync(async () => {
      try {
        const content = await $content('manuals', params.value.slug).fetch<any>();
        const [prev, next] = await $content('manuals').surround(params.value.slug).fetch<any>();
        return {
          content,
          prev,
          next,
        };
      } catch (err) {
        error({
          statusCode: 404,
        });
      }
    }, params.value.slug);

    const updateDate = computed(() => {
      return moment(pageData.value?.content?.updateDate).format('YYYY-MM-DD');
    });


    return {
      selectedMenu,
      selectedMenuItem,
      openKeys,
      pageData,
      updateDate,
      prevNextSelected,
      isRootSelected,
    };
  },
  head: {
    title: '用户手册',
    titleTemplate: '%s - 第三方风险排查系统',
  },
  render() {
    return (
      <div class={styles.container}>
        <div class={styles.sideBar}>
          <div class={styles.pageHead}>用户手册</div>
          <Menu
            style={styles.munu}
            mode="inline"
            defaultSelectedKeys={this.selectedMenu}
            defaultOpenKeys={this.openKeys}
            inlineIndent={15}
            onOpenChange={(keys) => {
              this.openKeys = keys;
            }}
          >
            {MENU_CONFIG.map((menu) => {
              if (menu.children) {
                return (
                  <Menu.SubMenu key={menu.key} class={{ 'submenu-selected': this.isRootSelected(menu.key) }}>
                    <div slot="title" class={styles.subMenu}>
                      <div class="menu-icon">
                        <IconArrow class="menu-icon__arrow" />
                      </div>
                      <nuxt-link to={menu.path}>{menu.label}</nuxt-link>
                    </div>
                    {menu.children.map((child) => (
                      <Menu.Item class={styles.menuItem} key={child.key}>
                        <div class="menu-icon">
                          <div class="menu-icon__dot"></div>
                        </div>
                        <nuxt-link to={child.path}>{child.label}</nuxt-link>
                      </Menu.Item>
                    ))}
                  </Menu.SubMenu>
                );
              }
              return (
                <Menu.Item class={styles.menuItem} key={menu.key}>
                  <div class={styles.icon}>
                    <div class={styles.dot}></div>
                  </div>
                  <nuxt-link to={menu.path}>{menu.label}</nuxt-link>
                </Menu.Item>
              );
            })}
          </Menu>
        </div>
        <div class={styles.main}>
          <div class={styles.wrapper}>
            <Breadcrumb class={styles.breadcurmb}>
              {this.selectedMenuItem.parent?.path ? (
                <Breadcrumb.Item>
                  <nuxt-link to={this.selectedMenuItem.parent.path}>
                    {this.selectedMenuItem.parent.label}
                  </nuxt-link>
                </Breadcrumb.Item>
              ) : null}
              <Breadcrumb.Item>{this.selectedMenuItem.label}</Breadcrumb.Item>
            </Breadcrumb>
            <DocSearch />
            <div style={{ flex: 1 }}>
              <div class={styles.doc}>
                <nuxt-content document={this?.pageData?.content} />
              </div>
              {this.selectedMenuItem.isRoot && this.selectedMenuItem.children ? (
                <ul class={styles.quickLink}>
                  {this.selectedMenuItem.children.map((child) => (
                    <li>
                      <nuxt-link to={child.path}>{child.label}</nuxt-link>
                    </li>
                  ))}
                </ul>
              ) : null}
            </div>
            <div class={styles.timeInfo}>
              最后更新于 {moment(this?.pageData?.content?.updateDate).format('YYYY-MM-DD')}
            </div>
            <PreNextArticle prev={this.prevNextSelected?.prev} next={this.prevNextSelected?.next} />
          </div>
        </div>
      </div>
    );
  },
});
export default GuideManual;
