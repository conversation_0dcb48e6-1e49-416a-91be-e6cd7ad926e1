.container {
  display: flex;
  justify-content: space-between;
  height: calc(100vh - 52px);

  .sideBar {
    width: 250px;
    background: #fff;
    overflow-y: auto;
    overflow-x: hidden;

    .pageHead {
      height: 78px;
      line-height: 78px;
      font-size: 20px;
      font-weight: bold;
      padding: 0 20px;
      background: url('../assets/icon-release-log-sider-header.png') no-repeat;
      background-size: 100%;
      // border-right: 1px solid #eee;
    }

    .menu-item,
    .sub-menu {
      display: flex;
      gap: 5px;
      width: 100%;
    }

    .sub-menu a {
      color: #333;
      white-space: break-spaces;
      line-height: 22px;
      flex: 1;
    }

    :global {
      .ant-menu {
        color: #333;
        overflow-y: auto;
        overflow-x: hidden;
      }

      .ant-menu-item {
        margin-top: 0;
        margin-bottom: 0;

        &:hover {
          background: #f2f8fe;
        }
      }

      .ant-menu-item,
      .ant-menu-submenu-title {
        display: flex;
        padding-top: 9px;
        padding-bottom: 9px;
        padding-right: 15px;
        height: fit-content;
        line-height: 22px;
        white-space: break-spaces;
      }

      .submenu-selected .ant-menu-submenu-title {
        background: #f2f8fe;

        &::after {
          content: '';
          border-right: 3px solid #128bed;
          position: absolute;
          right: 0;
          height: 100%;
          top: 0;
          bottom: 0;
        }
      }
      .ant-menu-item-selected {
        &::after {
          opacity: 1;
          transform: scaleY(1);
          border-right: 3px solid #128bed;
        }
      }

      .ant-menu-submenu-arrow {
        display: none;
      }

      .menu-icon {
        width: 16px;
        height: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #999999;
        transform: translateY(3px);
        &__dot {
          width: 4px;
          height: 4px;
          border-radius: 50%;
          background: currentColor;
        }
        &__arrow {
          width: 16px;
          height: 16px;
          transition: 0.3s transform;
          color: currentColor;
        }
      }

      .submenu-selected,
      .ant-menu-submenu-active {
        .menu-icon__arrow {
          color: #128bed;
        }
        .ant-menu-submenu-title {
          background: #f2f8fe;
          a {
            color: #128bed;
          }
        }
      }

      .ant-menu-item-selected,
      .ant-menu-item-active {
        .menu-icon__dot {
          background: #128bed;
        }
      }

      .ant-menu-submenu-open {
        .menu-icon__arrow {
          transform: rotate(-90deg);
        }
      }

      .ant-menu-inline,
      .ant-menu-vertical,
      .ant-menu-vertical-left {
        border-right: 0;
      }
    }
  }

  .main {
    flex: 1;
    overflow: auto;
    margin: 10px;
    background: #fff;
    padding: 20px 40px 60px;
    border-radius: 4px;

    .content {
      padding: 20px 0 60px;
      min-height: calc(100vh - 72px);
      border-radius: 4px;
      background: #fff;
      overflow-x: auto;
    }

    .wrapper {
      max-width: 720px;
      min-width: 650px;
      margin: 0px auto;
      min-height: calc(100vh - 152px);
      display: flex;
      flex-direction: column;
    }

    .breadcurmb {
      height: 50px;
      line-height: 50px;
      position: sticky;
      top: -20px;
      background: #fff;
      z-index: 10;
      color: #333333;
    }

    .doc {
      color: #333333;
      h1 {
        font-size: 32px;
        font-weight: 500;
        padding: 20px 0;
      }

      h2 {
        font-size: 20px;
        line-height: 28px;
        font-weight: 500;
        margin-bottom: 16px;
      }

      h3 {
        margin-bottom: 8px;
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
      }

      p {
        line-height: 22px;
        margin-bottom: 8px;

        & + h2 {
          margin-top: 40px;
        }
        & + h3 {
          margin-top: 16px;
        }
        &:last-child {
          margin-bottom: 0;
        }
      }

      img,
      video {
        display: block;
        border-radius: 8px;
        width: 100%;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        & + h2 {
          margin-top: 40px;
        }
        & + p,
        & + ol,
        & + h3 {
          margin-top: 16px;
        }
      }

      video {
        min-height: 405px;
      }

      ol li {
        list-style: decimal;
        margin-left: 16px;
        margin-bottom: 8px;
      }
    }

    .quick-link {
      border-radius: 8px;
      padding: 20px;
      border: 1px solid #eeeeee;
      font-size: 14px;
      line-height: 22px;
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-top: 40px;

      li {
        width: calc(50% - 10px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #333333;

        &:hover {
          color: #128bed;
        }
      }

      a {
        display: block;
        color: currentColor;
        position: relative;
        padding-left: 10px;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 4px;
          border-radius: 50%;
          background: currentColor;
        }
      }
    }

    .time-info {
      font-size: 14px;
      line-height: 22px;
      letter-spacing: 0em;
      color: #666666;
      padding: 14px 0;
      border-top: 1px solid #eee;
      margin-top: 40px;
      &:last-child {
        border-bottom: 1px solid #eee;
      }
    }
  }
}
