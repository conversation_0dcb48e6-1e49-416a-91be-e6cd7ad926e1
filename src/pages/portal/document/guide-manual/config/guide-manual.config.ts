export type MenuItem = {
  label: string;
  key: string;
  path: string;
  children?: MenuItem[];
  isRoot?: boolean;
};

export const MENU_CONFIG: MenuItem[] = [
  {
    label: '快速入门',
    key: 'quick-start',
    path: '/portal/document/guide-manual/quick-start',
    isRoot: true,
    children: [
      {
        label: '服务简介',
        key: 'service-intro',
        path: '/portal/document/guide-manual/service-intro',
      },
      {
        label: '系统登录',
        key: 'system-login',
        path: '/portal/document/guide-manual/system-login',
      },
      {
        label: '如何创建、管理用户',
        key: 'user-manage',
        path: '/portal/document/guide-manual/user-manage',
      },
      {
        label: '如何查看、管理权益',
        key: 'user-rights',
        path: '/portal/document/guide-manual/user-rights',
      },
      {
        label: '如何定义第三方企业分组、标签',
        key: 'label-manage',
        path: '/portal/document/guide-manual/label-manage',
      },
    ],
  },
  {
    label: '如何进行第三方企业的管理',
    key: 'third-party',
    path: '/portal/document/guide-manual/third-party',
    isRoot: true,
    children: [
      {
        label: '第三方企业添加',
        key: 'third-party-add',
        path: '/portal/document/guide-manual/third-party-add',
      },
      {
        label: '第三方企业删除',
        key: 'third-party-remove',
        path: '/portal/document/guide-manual/third-party-remove',
      },
      {
        label: '第三方企业更新',
        key: 'third-party-update',
        path: '/portal/document/guide-manual/third-party-update',
      },
      {
        label: '第三方企业查找',
        key: 'third-party-search',
        path: '/portal/document/guide-manual/third-party-search',
      },
      {
        label: '第三方企业分组、标签管理',
        key: 'third-party-group',
        path: '/portal/document/guide-manual/third-party-group',
      },
    ],
  },
  {
    label: '如何对第三方企业进行准入排查',
    key: 'investigation',
    path: '/portal/document/guide-manual/investigation',
    isRoot: true,
    children: [
      {
        label: '第三方企业准入排查',
        key: 'investigation-third',
        path: '/portal/document/guide-manual/investigation-third',
      },
      {
        label: '批量准入排查',
        key: 'investigation-batch',
        path: '/portal/document/guide-manual/investigation-batch',
      },
      {
        label: '历史排查记录',
        key: 'investigation-history',
        path: '/portal/document/guide-manual/investigation-history',
      },
      {
        label: '准入排查模型设置',
        key: 'investigation-setting',
        path: '/portal/document/guide-manual/investigation-setting',
      },
    ],
  },
  {
    label: '如何开展招标排查',
    key: 'bidding',
    path: '/portal/document/guide-manual/bidding',
    isRoot: true,
    children: [
      {
        label: '招标排查',
        key: 'bidding-investigation',
        path: '/portal/document/guide-manual/bidding-investigation',
      },
      {
        label: '批量招标排查',
        key: 'bidding-batch',
        path: '/portal/document/guide-manual/bidding-batch',
      },
      {
        label: '历史记录',
        key: 'bidding-history',
        path: '/portal/document/guide-manual/bidding-history',
      },
      {
        label: '招标排查模型设置',
        key: 'bidding-setting',
        path: '/portal/document/guide-manual/bidding-setting',
      },
    ],
  },
  {
    label: '如何对合作企业进行风险监控',
    key: 'monitor',
    path: '/portal/document/guide-manual/monitor',
    isRoot: true,
    children: [
      {
        label: '合作企业风险监控',
        key: 'monitor-add',
        path: '/portal/document/guide-manual/monitor-add',
      },
      {
        label: '风险动态管理',
        key: 'monitor-trends',
        path: '/portal/document/guide-manual/monitor-trends',
      },
      {
        label: '合作监控模型设置',
        key: 'monitor-setting',
        path: '/portal/document/guide-manual/monitor-setting',
      },
    ],
  },
  {
    label: '如何对第三方企业进行风险巡检',
    key: 'annual',
    path: '/portal/document/guide-manual/annual',
    isRoot: true,
    children: [
      {
        label: '第三方企业风险巡检',
        key: 'annual-setting',
        path: '/portal/document/guide-manual/annual-setting',
      },
      {
        label: '风险巡检结果',
        key: 'annual-review',
        path: '/portal/document/guide-manual/annual-review',
      },
      {
        label: '人工发起巡检',
        key: 'annual-rescan',
        path: '/portal/document/guide-manual/annual-rescan',
      },
      {
        label: '巡检记录',
        key: 'annual-history',
        path: '/portal/document/guide-manual/annual-history',
      },
    ],
  },
  {
    label: '如何进行特定利益关系排查',
    key: 'interest',
    path: '/portal/document/guide-manual/interest',
    isRoot: true,
    children: [
      {
        label: '特定利益关系排查',
        key: 'interest-investigation',
        path: '/portal/document/guide-manual/interest-investigation',
      },
      {
        label: '批量特定利益关系排查',
        key: 'interest-batch',
        path: '/portal/document/guide-manual/interest-batch',
      },
      {
        label: '历史记录',
        key: 'interest-history',
        path: '/portal/document/guide-manual/interest-history',
      },
      {
        label: '特定利益关系排查模型设置',
        key: 'interest-setting',
        path: '/portal/document/guide-manual/interest-setting',
      },
    ],
  },
  {
    label: '如何进行潜在利益冲突排查',
    key: 'potential',
    path: '/portal/document/guide-manual/potential',
    isRoot: true,
    children: [
      {
        label: '内部员工管理',
        key: 'potential-staff',
        path: '/portal/document/guide-manual/potential-staff',
      },
      {
        label: '潜在利益冲突排查',
        key: 'potential-investigation',
        path: '/portal/document/guide-manual/potential-investigation',
      },
      {
        label: '历史记录',
        key: 'potential-history',
        path: '/portal/document/guide-manual/potential-history',
      },
      {
        label: '潜在利益冲突排查模型设置',
        key: 'potential-setting',
        path: '/portal/document/guide-manual/potential-setting',
      },
    ],
  },
  {
    label: '其他',
    key: 'others',
    path: '/portal/document/guide-manual/others',
    isRoot: true,
    children: [
      {
        label: '工作台',
        key: 'dashboard',
        path: '/portal/document/guide-manual/dashboard',
      },
      {
        label: '分析看板',
        key: 'analysis-dashborad',
        path: '/portal/document/guide-manual/analysis-dashborad',
      },
    ],
  },
];

export const FLAT_MENU_CONFIG = MENU_CONFIG.reduce((acc, cur) => {
  if (cur.children) {
    acc.push(
      cur,
      ...cur.children.map((v) => ({
        ...v,
        parent: cur,
      }))
    );
  } else {
    acc.push(cur);
  }
  return acc;
}, [] as any[]);
