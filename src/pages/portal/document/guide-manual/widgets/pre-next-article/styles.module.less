.container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  border-top: 1px solid #eeeeee;
  border-bottom: 1px solid #eeeeee;

  .label {
    font-size: 14px;
    line-height: 22px;
    color: #666666;
    margin-bottom: 4px;
  }

  .link {
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: #333333;
  }
}
.prev,
.next {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 14px 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;

  &:hover {
    background: #f2f8fe;
  }

  svg {
    width: 16px;
    height: 16px;
    color: #999999;
  }

  .content {
    flex: 1;
  }
}

.prev + .next {
  position: relative;
  &::before {
    content: '';
    position: absolute;
    top: 14px;
    bottom: 14px;
    left: 0;
    width: 1px;
    background: #eeeeee;
  }
}

.prev {
  svg {
    transform: rotate(180deg);
  }
}
