import { defineComponent } from 'vue';
import styles from './styles.module.less';
import IconArrow from '@/assets/icons/icon-wenzilianjiantou.svg?inline';

const PreNextArticle = defineComponent({
  name: 'PreNextArticle',
  props: {
    prev: {
      type: Object,
      required: false,
    },
    next: {
      type: Object,
      required: false,
    },
  },
  render() {
    if (!this.prev && !this.next) {
      return null;
    }
    return (
      <div class={styles.container}>
        {this.prev ? (
          <nuxt-link class={styles.prev} to={this.prev.path}>
            <IconArrow />
            <div class={styles.content}>
              <div class={styles.label}>上一篇</div>
              <div class={styles.link}>
                {this.prev.label}
              </div>
            </div>
          </nuxt-link>
        ) : null}
        {this.next ? (
          <nuxt-link class={styles.next} to={this.next.path}>
            <div class={styles.content}>
              <div class={styles.label}>下一篇</div>
              <div class={styles.link}>
                {this.next.label}
              </div>
            </div>
            <IconArrow />
          </nuxt-link>
        ) : null}
      </div>
    );
  },
});

export default PreNextArticle;
