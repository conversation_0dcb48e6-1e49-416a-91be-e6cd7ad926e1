import { useContext } from '@nuxtjs/composition-api';
import { Input } from 'ant-design-vue';
import Fuse from 'fuse.js';
import { defineComponent, onMounted, ref } from 'vue';
import { stripMarkdown } from '@/utils/strip-markdown';

const DocSearch = defineComponent({
  name: 'DocSearch',
  props: {},
  setup() {
    const { $content } = useContext();

    const fuseHandler = ref(null);
    const searchList = ref([]);
    const initSearch = async () => {
      const searchData = await $content('manuals', { text: true })
        .only(['text', 'title', 'slug'])
        .fetch<any>();
      const stripedData = searchData.map((item) => {
        return {
          ...item,
          text: stripMarkdown(item.text),
        };
      });
      console.log(stripedData);
      const fuse = new Fuse(stripedData, {
        keys: ['title', 'text'],
        includeMatches: true,
        minMatchCharLength: 2,
      });

      fuseHandler.value = fuse;
    };

    const handleSearch = (keywords) => {
      const res = fuseHandler.value.search(keywords);
      console.log(res);
      searchList.value = res;
    };

    onMounted(() => {
      initSearch();
    });
    return {
      handleSearch,
      searchList,
    };
  },
  render() {
    return (
      <div>
        <Input.Search placeholder="请输入内容关键词，如：准入排查、招标排查" onSearch={this.handleSearch} />
        <ul>
          {this.searchList.map(({ item }) => {
            return (
              <li key={item.slug}>
                {item.matches.map(({ key, value, indices }) => {
                  const htmlStr = value;
                  if (key === 'title') {
                    return <nuxt-link to={item.slug} domPropsInnerHTML={htmlStr}></nuxt-link>;
                  }
                  if (key === 'text') {
                    return <p domPropsInnerHTML={htmlStr}></p>;
                  }
                  return null;
                })}
              </li>
            );
          })}
        </ul>
      </div>
    );
  },
});

export default DocSearch;
