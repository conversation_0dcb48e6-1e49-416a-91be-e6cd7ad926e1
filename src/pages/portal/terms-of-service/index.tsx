import { computed, defineComponent, ref } from '@nuxtjs/composition-api';
import { Button, Dropdown, Icon, Menu } from 'ant-design-vue';
import { TERMS_OF_SERVICE_CONTENT, TERMS_VERSION_OPTIONS } from './config/terms';
import styles from './styles.module.less';
import LandingSection from '@/features/landing-section';
import LandingWrapper from '@/features/landing-wrapper';

const TermsOfServicePage = defineComponent({
  name: 'TermsOfServicePage',
  layout: 'portal',
  setup() {
    const versions = computed(() => TERMS_VERSION_OPTIONS);
    const currentVersion = ref(versions.value[0].value);
    const setCurrentVersion = (version: string) => {
      currentVersion.value = version;
    };
    const currentContent = computed(() => {
      return TERMS_OF_SERVICE_CONTENT.find((item) => item.version === currentVersion.value)?.content;
    });
    return {
      versions,
      currentContent,
      currentVersion,
      setCurrentVersion,
    };
  },
  render() {
    return (
      <LandingSection title="服务条款">
        <LandingWrapper>
          <div class={styles.container}>
            <Dropdown class={styles.select}>
              <Button>
                <span>{this.currentVersion}</span>
                <Icon type="caret-down" />
              </Button>
              <Menu slot="overlay">
                {this.versions.map(({ value, label }) => {
                  return (
                    <Menu.Item key={value} onClick={() => this.setCurrentVersion(value)}>
                      {label}
                    </Menu.Item>
                  );
                })}
              </Menu>
            </Dropdown>
            <div class={styles.content} domPropsInnerHTML={this.currentContent} />
          </div>
        </LandingWrapper>
      </LandingSection>
    );
  },
});

export default TermsOfServicePage;
