import { defineComponent, ref, unref, watch } from 'vue';
import { Modal } from 'ant-design-vue';
import styles from './styles.module.less';

const INTRO_VIDEO_URL = '//qcc-static.qcc.com/rover/public/video/intro-720.mp4';

const IntroVideoModal = defineComponent({
  name: 'IntroVideoModal',
  model: {
    prop: 'visible',
    event: 'change',
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    width: {
      type: String,
      default: '960px',
    },
  },
  emits: ['change'],
  setup(props, { emit }) {
    const video = ref<HTMLVideoElement | null>(null);
    const controlVideo = (type: 'play' | 'pause') => {
      const rawVideo = unref(video);
      rawVideo?.[type]?.();
    };

    const handleAutoPlay = (event) => {
      event?.target?.play();
    };

    // `visible` 为 false 时，暂停播放视频
    watch(
      () => props.visible,
      (isVisible) => {
        controlVideo(isVisible ? 'play' : 'pause');
      }
    );

    const handleClose = () => {
      emit('change', false);
    };

    return {
      video,
      handleAutoPlay,
      handleClose,
    };
  },
  render() {
    return (
      <Modal
        title="系统介绍视频"
        visible={this.visible}
        width={this.width}
        wrapClassName={styles.wrapper}
        onCancel={this.handleClose}
        footer={null}
        centered
      >
        <video
          ref="video"
          src={INTRO_VIDEO_URL}
          width="100%"
          height="100%"
          controls
          onCanplay={this.handleAutoPlay}
        >
          抱歉，您的浏览器不支持内嵌视频，不过不用担心，你可以
          <a href={INTRO_VIDEO_URL}>下载</a>
          并用你喜欢的播放器观看！
        </video>
      </Modal>
    );
  },
});

export default IntroVideoModal;
