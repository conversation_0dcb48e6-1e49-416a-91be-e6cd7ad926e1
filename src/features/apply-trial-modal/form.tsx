import { get } from 'lodash';
import { defineComponent, ref } from 'vue';
import { Form, Input, message } from 'ant-design-vue';
import CompanySuggestionInput from '../company-suggestion-input';
import NoCaptcha from '@/components/no-captcha';
import CountDown from '@/components/count-down';
import { stripHTMLTags } from '@/utils/strip-html-tags';
import { service } from '@/services';
import { useNoCaptcha } from '@/components/no-captcha/use-no-captcha';

const ApplyTrialForm = defineComponent({
  name: 'ApplyTrialForm',
  props: {
    form: {
      type: Object,
      required: false,
    },
  },
  setup(props) {
    const request = (keywords: string) => {
      return service.company
        .companyNameSuggestion({
          searchKey: keywords,
          pageSize: 5,
        })
        .then(({ data = {} }) => data?.Result ?? [])
        .then((result) =>
          result.map((item) => {
            return {
              id: item?.KeyNo,
              value: item?.Name ? stripHTMLTags(item?.Name) : undefined, // text
              label: item?.Name, // html
            };
          })
        );
    };

    const [verifyToken, isCaptchaValid, handleCaptchaSuccess] = useNoCaptcha();
    const noCaptchaRef = ref();

    /**
     * 发送验证码
     */
    const handleSendVerifyCode = async (): Promise<boolean> => {
      const { companyName, name, phone } = props.form.getFieldsValue();
      if (!companyName) {
        message.warning('请输入企业名称');
        return false;
      }
      if (!name) {
        message.warning('请输入联系人姓名');
        return false;
      }
      if (!verifyToken.value) {
        message.warning('请拖动滑块进行人机验证');
        return false;
      }
      if (!phone) {
        message.warning('请输入正确的手机号码');
        return false;
      }
      try {
        const { data } = await service.user.sendVerifyCodeBySMS({
          phone,
          token: verifyToken.value,
        });
        if (data?.code !== 'OK') {
          message.error('验证码发送失败，请重试');
          return;
        }
        message.success('验证码发送成功');
        return true;
      } catch (error) {
        message.error('验证码发送失败，请重试');
        const code = get(error, 'response.data.code');
        if (code === 200502) {
          noCaptchaRef.value?.onReset?.();
        }
        return false;
      }
    };

    return {
      handleCaptchaSuccess,
      request,
      isCaptchaValid,

      handleSendVerifyCode,
    };
  },
  render() {
    const labelCol = { span: 4 };
    const wrapperCol = { span: 20 };

    return (
      <Form form={this.form} colon={false} layout="horizontal">
        <Form.Item label="企业名称" labelCol={labelCol} wrapperCol={wrapperCol}>
          <CompanySuggestionInput
            placeholder="请输入企业名称"
            size="large"
            allowClear
            remote={this.request}
            v-decorator={[
              'companyName',
              {
                rules: [{ required: true, message: '请输入企业名称' }],
              },
            ]}
          />
        </Form.Item>
        {/* <Form.Item label="部门" labelCol={labelCol} wrapperCol={wrapperCol}>
          <Input placeholder="请输入部门" size="large" allowClear />
        </Form.Item> */}
        <Form.Item label="联系人姓名" labelCol={labelCol} wrapperCol={wrapperCol} required>
          <Input
            placeholder="请输入联系人姓名"
            size="large"
            allowClear
            v-decorator={[
              'name',
              {
                rules: [{ required: true, message: '请输入联系人姓名' }],
              },
            ]}
          />
        </Form.Item>
        <Form.Item label="联系人电话" labelCol={labelCol} wrapperCol={wrapperCol} required>
          <Input
            placeholder="请输入联系人手机号"
            size="large"
            allowClear
            autoComplete="new-password"
            addon-before={'中国 +86'}
            maxLength={11}
            v-decorator={[
              'phone',
              {
                rules: [
                  {
                    required: true,
                    pattern: /^1[3-9]\d{9}$/,
                    message: '请输入正确的手机号码',
                  },
                ],
              },
            ]}
          />
        </Form.Item>
        {/* noCaptcha */}
        <Form.Item label=" " labelCol={labelCol} wrapperCol={wrapperCol}>
          <NoCaptcha
            id="apply-trial-no-captcha"
            ref="noCaptchaRef"
            sceneId="1432ga7w"
            size="lg"
            onSuccess={this.handleCaptchaSuccess}
          />
        </Form.Item>
        <Form.Item label="短信验证码" labelCol={labelCol} wrapperCol={wrapperCol} required>
          <Input
            placeholder="请输入验证码"
            size="large"
            allowClear
            maxLength={6}
            v-decorator={[
              'code',
              {
                rules: [
                  {
                    required: true,
                    pattern: /^\d{6}$/,
                    message: '请输入验证码',
                  },
                ],
              },
            ]}
          >
            <CountDown
              slot="addonAfter"
              placeholder="获取验证码"
              disabled={!this.isCaptchaValid}
              action={this.handleSendVerifyCode}
            />
          </Input>
        </Form.Item>
        <Form.Item label="备注" labelCol={labelCol} wrapperCol={wrapperCol}>
          <Input.TextArea
            v-decorator={['remarks', { initialValue: '' }]}
            placeholder={'请输入您的需求'}
            maxLength={2000}
            rows={3}
            style={'height: 100px;'}
          />
        </Form.Item>
      </Form>
    );
  },
});

export default Form.create({})(ApplyTrialForm);
