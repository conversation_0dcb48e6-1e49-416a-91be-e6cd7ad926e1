import { defineComponent, ref, unref, useContext } from '@nuxtjs/composition-api';
import { Button, Modal, message } from 'ant-design-vue';
import axios from 'axios';
import ApplyTrialForm from './form';
import styles from './styles.module.less';
import CloseIcon from '@/assets/icons/icon-tanchuangguanbi.svg?inline';

const ApplyTrialModal = defineComponent({
  name: 'ApplyTrialModal',
  model: {
    prop: 'visible',
    event: 'change',
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    width: {
      type: String,
      default: '600px',
    },
    channel: {
      type: String,
      required: true,
    },
  },
  emits: ['change', 'success'],
  setup(props, { emit }) {
    const { $service } = useContext();
    const submitStatus = ref<'idle' | 'pending' | 'success' | 'error'>('idle');
    const formRef = ref<any | null>(null);

    const strategies = {
      // 提交成功
      success: () => {
        submitStatus.value = 'success';
        // 成功提示消息
        Modal.success({
          title: '提交成功！客户经理将在 1-3 个工作日内与您联系，请保持电话畅通。',
          okText: '知道了',
        });
        // 关闭弹窗口
        emit('change', false);
        // 提交成功
        emit('success');
      },

      // Axios error
      axiosError: (error) => {
        if (error?.response?.data?.code === 200416) {
          // 已开通帐号
          message.warning('当前手机号已开通，无需重复申请');
        } else {
          // 错误消息
          message.error(error?.response?.data?.error ?? '操作出错，请稍后再试');
        }
      },

      // 其它错误
      commonError: (error) => {
        message.warning(error?.message ?? '操作出错，请稍后再试');
      },
    };

    /**
     * 表单验证
     * @param formError
     * @param formData
     */
    const formValidator = async (formError, formData) => {
      if (formError) {
        message.error('有必填项还未填写！');
        return;
      }

      try {
        submitStatus.value = 'pending';
        const res = await $service.user.trialApply({
          ...formData,
          channel: props.channel, // 来源渠道: 在CRM的`线索`接收试用申请
        });
        if (res?.data?.status === '200') {
          strategies.success();
        } else {
          throw new Error('操作出错，请稍后再试');
        }
      } catch (error) {
        submitStatus.value = 'error';
        if (axios.isAxiosError(error)) {
          strategies.axiosError(error);
        } else {
          strategies.commonError(error);
        }
      }
    };

    /**
     * 提交表单
     */
    const handleFormSubmit = () => {
      const formRefRaw = unref(formRef);
      formRefRaw?.validateFields(formValidator);
    };

    const handleClose = () => {
      emit('change', false);
    };

    return {
      formRef,
      handleFormSubmit,
      handleClose,
      submitStatus,
    };
  },
  render() {
    return (
      <Modal
        visible={this.visible}
        width={this.width}
        wrapClassName={styles.wrapper}
        onCancel={this.handleClose}
        centered
        destroyOnClose
      >
        <div class={styles.header} slot="title">
          <div>申请试用</div>
          {this.$slots?.tooltip ?? null}
        </div>
        <div slot="footer">
          <Button key="back" class={styles.cancelButton} onClick={this.handleClose}>
            取消
          </Button>
          <Button
            key="submit"
            type="primary"
            onClick={this.handleFormSubmit}
            loading={this.submitStatus === 'pending'}
            class={styles.sureButton}
          >
            提交
          </Button>
        </div>

        <CloseIcon slot="closeIcon" />

        <div class={styles.tip}>
          <p>申请提交成功后，客户经理会在1-3个工作日内与您联系。</p>
          <p>
            如有疑问，请致电联系客服：<em>400-088-8275</em>
          </p>
        </div>

        <div class={styles.form}>
          <ApplyTrialForm ref="formRef" />
        </div>
      </Modal>
    );
  },
});

export default ApplyTrialModal;
