import { debounce } from 'lodash';
import { Input } from 'ant-design-vue';
import { PropType, computed, defineComponent, ref } from 'vue';
import { onClickOutside } from '@vueuse/core';
import styles from './styles.module.less';

const CompanySuggestionInput = defineComponent({
  name: 'CompanySuggestionInput',
  model: {
    event: 'change',
    prop: 'value',
  },
  props: {
    /**
     * 关键字值
     */
    value: {
      type: String,
      required: false,
    },
    /**
     * 接口请求
     */
    remote: {
      type: Function,
      required: false,
    },
    /**
     * 控件尺寸
     */
    size: {
      type: String as PropType<Input['size']>,
      default: 'default',
    },
    /**
     * 占位符
     */
    placeholder: {
      type: String,
      required: false,
    },
    /**
     * 支持清除
     */
    allowClear: {
      type: Boolean,
      default: false,
    },
    /**
     * 自动聚焦: 暂未支持
     */
    // autoFocus: {
    //   type: Boolean,
    //   default: false,
    // },
  },
  emits: ['change', 'select'],
  // expose: ['focus'],
  setup(props, { emit }) {
    /**
     * 值变事件
     * @param value
     * @param item
     */
    const emitChange = (value: string, item?: object) => {
      emit('change', value, item);
    };

    /**
     * 选择事件
     * @param value
     * @param item
     */
    const emitSelect = (value: string, item?: object) => {
      emit('select', value, item);
    };

    const container = ref<HTMLInputElement | null>(null);
    const input = ref<HTMLInputElement | null>(null);

    const result = ref<Array<any>>([]);

    const isExpand = ref(false);
    onClickOutside(container, () => {
      isExpand.value = false;
    });

    const _request = async (keywords: string) => {
      if (keywords.length < 2 || typeof props.remote !== 'function') {
        result.value = [];
        return;
      }
      try {
        const res = await props.remote(keywords);
        result.value = res;
      } catch (error) {
        console.error(error);
        result.value = [];
      }
    };
    const request = debounce(_request, 300, {
      trailing: true,
    });

    const handleKeywordsChange = (event) => {
      const keywords = event.target?.value?.trim() ?? '';
      emitChange(keywords);
      request(keywords);
    };

    const handleFocus = () => {
      isExpand.value = true;
    };

    const handleClear = () => {
      emitChange('');
    };

    const handleInputEnter = () => {
      emitChange(props.value);
    };

    const handleSelect = (value, item) => {
      emitChange(value, item);
      emitSelect(value, item);
      isExpand.value = false;
    };

    const isResultVisible = computed(() => {
      return isExpand.value && props.value && props.value.length >= 2 && result.value.length > 0;
    });

    return {
      input,
      container,
      handleKeywordsChange,
      handleInputEnter,
      handleFocus,
      handleClear,

      result,
      isResultVisible,

      handleSelect,
    };
  },
  render() {
    return (
      <div ref="container" class={styles.container}>
        <Input
          placeholder={this.placeholder}
          value={this.value}
          onFocus={this.handleFocus}
          onChange={this.handleKeywordsChange}
          onPressEnter={this.handleInputEnter}
          size={this.size}
          allowClear={this.allowClear}
        />

        <div class={styles.body} v-show={this.isResultVisible}>
          <div class={styles.result}>
            <ul>
              {/* 支持 scopedSlots */}
              {this.result.map((item) => {
                return (
                  <li class={styles.item} key={item.id} onClick={() => this.handleSelect(item.value, item)}>
                    <div class={styles.main}>
                      {item.logo ? (
                        <i class={styles.logo}>
                          <img width="22" height="22" src={item.logo} />
                        </i>
                      ) : null}
                      <span domPropsInnerHTML={item.label} />
                    </div>
                    {/* <div>
                      <span class={styles.type}>公司名称</span>
                    </div> */}
                  </li>
                );
              })}
            </ul>
          </div>
        </div>
      </div>
    );
  },
});

export default CompanySuggestionInput;
