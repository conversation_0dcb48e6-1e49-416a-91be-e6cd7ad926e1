@import '../../assets/styles/token.less';

.container {
  position: relative;
  .body {
    position: absolute;
    z-index: 100;
    left: 0;
    right: 0;
    background: #fff;
    box-shadow: 0px 3px 12px 0 rgba(0, 0, 0, 0.12);
    padding: 15px;
  }
  .result {
    background: #f8fbfe;
  }

  .logo {
    width: 22px;
    height: 22px;
    border-radius: 2px;
    overflow: hidden;
    margin-right: 5px;
    border: 1px solid #eee;
    background: #fff;
    display: inline-flex;
    img {
      width: 100%;
      height: 100%;
    }
  }

  .item {
    font-size: 14px;
    line-height: 22px;
    padding: 8px;
    display: flex;
    justify-content: space-between;
    em {
      color: @base-color-red-500;
    }
    &:hover {
      cursor: pointer;
      color: @base-color-blue-500;
      em {
        color: inherit;
      }
      .type {
        color: inherit;
        background: #e5f2fd;
      }
    }
  }

  .main {
    display: flex;
    flex: 1;
  }

  .type {
    font-weight: 400;
    background: #f6f6f6;
    color: #999;
    white-space: nowrap;
    font-size: 12px;
    line-height: 18px;
    padding: 2px 6px;
  }
}
