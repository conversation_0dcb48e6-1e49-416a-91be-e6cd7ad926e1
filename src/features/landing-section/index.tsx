import { defineComponent, PropType } from 'vue';
import styles from './styles.module.less';

const LandingSection = defineComponent({
  functional: true,
  props: {
    title: {
      type: String,
      required: true,
    },
    description: {
      type: String,
    },
    theme: {
      type: String as PropType<'default' | 'gray' | 'blue' | 'cloudy'>,
      default: 'default',
    },
  },
  render(h, { props, children, data }) {
    return (
      <div
        class={{
          [styles.container]: true,
          [styles[props.theme]]: true,
        }}
        {...{
          attrs: data.attrs,
        }}
      >
        <header class={styles.header}>
          <h3 class={styles.title}>{props.title}</h3>
          <p class={styles.description}>{props.description}</p>
        </header>
        <div>{children}</div>
      </div>
    );
  },
});

export default LandingSection;
