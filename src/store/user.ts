import type { ActionTree, GetterTree, MutationTree } from 'vuex/types';
import { get } from 'lodash';
import { RootState } from '.';
import { service } from '@/services';

type UserState = {
  profile: { [key: string]: any } | null;
};

export const getters: GetterTree<UserState, RootState> = {
  isLogin: ({ profile }) => {
    if (!profile) {
      return false;
    }
    return get(profile, 'isLogin', false);
  },
  isZeiss: ({ profile }) => {
    if (!profile) {
      return false;
    }
    return get(profile, 'groupVersion') === 'v2';
  },
};

export const mutations: MutationTree<UserState> = {
  SET_PROFILE(state, profile) {
    state.profile = profile;
  },
};

export const actions: ActionTree<UserState, RootState> = {
  async updateProfile({ commit }) {
    try {
      const { data } = await service.user.profile();
      commit('SET_PROFILE', data);
      return data;
    } catch (error) {
      // console.error(error);
      commit('SET_PROFILE', null);
      return null;
    }
  },
};

export const state = (): UserState => ({
  profile: null,
});
