import { Store } from 'vuex/types';
import { InjectionKey } from 'vue/types';

export type UserProfile = {
  id: number;
  guid: string;
  name: string;
  username: string;
  nickname: string;
  currentOrg: number;
  organizationId: number;
  userId: number;
  accountId: number;
  phone: string;
  email: string;
  bundle: object;
  bundleError: object;
  isLogin?: number;
};

export interface RootState {
  user: {
    profile: UserProfile | undefined;
  };
}

export const key: InjectionKey<Store<RootState>> = Symbol('store');
