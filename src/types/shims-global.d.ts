// 验证码模式枚举
type CaptchaMode = 'popup' | 'embed';

// 验证码语言类型
type CaptchaLanguage = 'cn' | 'en' | 'ja' | 'ko' | 'th' | 'vi' | 'fr' | 'ru' | 'ar' | 'de' | 'it' | 'he' | 'hi' | 'id' | 'my';

// 滑块样式配置
interface SlideStyle {
  width?: number;
  height?: number;
}

// 验证码配置参数
interface AliyunCaptchaConfig {
  /** 验证码场景ID，新建验证场景后可获取该值 */
  SceneId: string;
  /** 验证码模式：popup（弹出式）或 embed（嵌入式） */
  mode: CaptchaMode;
  /** 页面上预留渲染验证码的元素，与源代码中预留的页面元素保持一致 */
  element: string;
  /** 触发验证码弹窗或无痕验证的元素 */
  button: string;
  /** 验证码验证通过回调函数 */
  success: (captchaVerifyParam: string) => void;
  /** 验证码验证失败回调函数（可选） */
  fail?: (errorCode: string | number) => void;
  /** 绑定验证码实例回调函数 */
  getInstance: (instance: any) => void;
  /** 滑块验证和一点即过的验证形态触发框体样式（可选） */
  slideStyle?: SlideStyle;
  /** 验证码语言类型（可选，默认为 cn） */
  language?: CaptchaLanguage;
  /** 验证码初始化请求单次请求超时时间，单位为毫秒（可选，默认为 5000） */
  timeout?: number;
  /** 对验证码UI进行整体缩放（可选，默认为 1） */
  rem?: number;
  /** 验证码初始化接口请求和验证码资源加载失败、超时的错误回调函数（可选） */
  onError?: (error: any) => void;
  /** 验证码弹窗关闭时触发的回调函数（可选） */
  onClose?: () => void;
  /** 企业Logo更换参数，为图片URL链接或者base64格式数据（可选） */
  captchaLogoImg?: string;
}

declare global {
  interface Window {
    initAliyunCaptcha?: (config: AliyunCaptchaConfig) => void; // 阿里云验证码
  }
}

export {};
