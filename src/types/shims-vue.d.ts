import { defineComponent } from 'vue';
import type { ComponentPublicInstance, VNode } from 'vue';
import type { ThisTypedMountOptions } from '@vue/test-utils';

declare module '*.vue' {
  const Component: ReturnType<typeof defineComponent>;
  export default Component;
}

/**
 * 解决 `domPropsInnerHTML` 类型错误
 */
declare module 'vue/types/jsx' {
  export interface HTMLAttributes {
    domPropsInnerHTML?: unknown;
  }
}

/**
 * 解决 `onClick` 类型报错
 * https://github.com/vuejs/vue/issues/12680#issuecomment-1211672009
 */
declare module 'vue/types/vnode' {
  export interface ComponentCustomProps {
    [key: string]: any;
  }
}

/**
 * 解决 `@vue/test-utils` 的类型推导报错
 */
declare module '@vue/test-utils' {
  export function mount<V extends {}>(
    originalComponent: {
      new (...args: any[]): V;
    },
    options?: ThisTypedMountOptions<V>
  ): Wrapper<ComponentPublicInstance<V>>;

  export function shallowMount<V extends {}>(
    originalComponent: {
      new (...args: any[]): V;
    },
    options?: ThisTypedMountOptions<V>
  ): Wrapper<ComponentPublicInstance<V>>;
}
