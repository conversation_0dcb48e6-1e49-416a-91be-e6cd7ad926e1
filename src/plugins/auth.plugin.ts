import { Plugin } from '@nuxt/types';

const AuthPlugin: Plugin = async ({ route, store, redirect }) => {
  try {
    await store.dispatch('user/updateProfile');
    // 判断如果为登录状态则跳转到首页
    const REDIRECT_TO_HOME = ['/portal/login', '/portal/reset-password'];
    const isLogin = store.getters['user/isLogin'];
    if (isLogin && REDIRECT_TO_HOME.includes(route.path)) {
      if (process.server) {
        redirect('/');
      }
      if (process.client) {
        // https://v2.nuxt.com/docs/internals-glossary/context#redirect
        window.onNuxtReady(() => {
          window.$nuxt.$router.replace('/');
        });
      }
    }
  } catch (error) {
    console.error(error);
  }
};

export default AuthPlugin;
