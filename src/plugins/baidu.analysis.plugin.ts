import { Plugin } from '@nuxt/types';

const BaiduAnalysisPlugin: Plugin = ({ app: { router } }) => {
  router.afterEach((to) => {
    if (process.env.RUNTIME_ENV === 'prod') {
      // 仅在生产环境执行
      try {
        window._hmt = window._hmt || [];
        window._hmt.push(['_trackPageview', to.fullPath]);
      } catch (error) {}
    }
  });
};
export default BaiduAnalysisPlugin;

declare global {
  interface Window {
    _hmt?: [string, string][];
  }
}
