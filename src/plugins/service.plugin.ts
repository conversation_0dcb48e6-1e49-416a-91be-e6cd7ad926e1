import type { Plugin } from '@nuxt/types';
import { wrapProperty } from '@nuxtjs/composition-api';
import { service } from '@/services';

type ServiceType = typeof service;

/**
 * Context API style
 * @example `const { $service } = useContext()`
 */
const ServicePlugin: Plugin = (_, inject) => {
  const $service = service;
  inject('service', $service);
};

/**
 * Composition API style
 * @example `const $service = useService()`
 */
export const useService = wrapProperty('$service', false);

export default ServicePlugin;

// Nuxt 2.9+
declare module '@nuxt/types' {
  interface Context {
    $service: ServiceType;
  }

  interface NuxtAppOptions {
    $service: ServiceType;
  }
}

declare module 'vue/types/vue' {
  interface Vue {
    $service: ServiceType;
  }
}
