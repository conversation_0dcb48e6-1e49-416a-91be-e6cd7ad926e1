/* eslint-disable no-console */
import { TraceData, TraceEvent } from "./trace-data";
import { TraceMessenger } from "./trace-messenger";

export interface TracerConfig {
  appName: string;
  applicationName: string;
  sdk: string;
  beforeSend?: () => void
  enable?: boolean;
  debug?: boolean;
}

/**
 * 行为追踪
 */
export class Tracer {
  private appName: string;
  private applicationName: string;
  private sdk: string;
  messenger: TraceMessenger;
  enable: boolean;
  debug: boolean;

  constructor(config: TracerConfig) {
    this.appName = config.appName;
    this.applicationName = config.applicationName;
    this.sdk = config.sdk;
    this.enable = config.enable || false;
    this.debug = config.debug || false;
  }

  /**
   * 追踪事件
   */
  track(data?: Pick<TraceData, 'event' | 'entity'>) {
    const traceData = {
      appName: this.appName,
      originUrl: document.location.href,
      event: data.event,
      entity: data.entity,
      // entity: entity && this.userId ? { ...entity, uid: this.userId } : entity,
      // ref?: string;
      // uid?: string; // 用户id, 非 qcc.com 用户体系的情况下传
    } satisfies TraceData;

    setTimeout(() => {
      if (this.enable) {
        this.messenger.send(traceData);
      }
      if (this.debug) {
        this.log(traceData)
      }
    }, 0);
  }

  /**
   * 页面访问追踪
   * @param pageName 当前页面名称
   */
  pageView(pageName: string) {
    this.track({
      event: TraceEvent.PageView,
      entity: {
        application_name: this.applicationName,
        page_name: pageName,
      },
    })
  }

  /**
   * 点击事件追踪
   * @param pageName 当前页面名称
   * @param buttonName 点击区块名称
   */
  clickEvent(pageName: string, buttonName: string) {
    this.track({
      event: TraceEvent.ClickEvent,
      entity: {
        application_name: this.applicationName,
        page_name: pageName,
        button_name: buttonName,
      },
    });
  }

  /**
   * 搜索事件追踪
   * @param pageName 当前页面名称
   * @param searchType 搜索类型
   * @param keywordsDetail 搜索关键字
   * @param objectType 对象类型
   * @param objectDetail 对象具体参数
   */
  searchEvent(
    pageName: string,
    searchType: string,
    keywordsDetail?: string,
    objectType?: string,
    objectDetail?: any,
  ) {
    this.track({
      event: TraceEvent.SearchEvent,
      entity: {
        application_name: this.applicationName,
        page_name: pageName,
        search_type: searchType,
        searchwords_detail: keywordsDetail,
        object_type: objectType,
        object_detail: objectDetail,
      },
    });
  }

  /**
   * 调试日志
   * @param payload
   */
  private log(payload) {
    console.group(`%c[TRACER]::${payload?.event} ${new Date().toLocaleString()}`, 'color:#CD6155');
    console.log(JSON.stringify(payload, null, 2));
    console.groupEnd();
  }
}
