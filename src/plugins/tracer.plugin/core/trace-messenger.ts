import { TraceData } from "./trace-data";

export abstract class Trace<PERSON>essenger {
  protected sdk: string;
  abstract normalize(data: TraceData): string;
  abstract send(data: TraceData): void;
}

/**
 * ImageTraceMessenger
 */
export class ImageTraceMessenger extends TraceMessenger {
  constructor(sdk: string) {
    super();
    this.sdk = `${sdk}/s.gif`;
  }

  /**
   * 数据格式化为 query string
   * @param {TraceData} data
   */
  normalize(data: TraceData): string {
    const params = Object.keys(data)
      .map((key) => {
        let value = data[key];
        if (value && key === 'entity') {
          value = JSON.stringify(data[key]);
        }
        return [key, encodeURIComponent(value)].join('=');
      })
      .concat(`t=${Date.now()}`) // 增加时间戳
      .join('&');
    return params;
  }

  /**
   * 发起追踪事件
   * @param {TraceData} data
   */
  send(data: TraceData) {
    const src = `${this.sdk}?${this.normalize(data)}`;
    const img = document.createElement('img');
    img.src = src;
  }
}
