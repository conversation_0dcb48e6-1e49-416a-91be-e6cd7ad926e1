import { Plugin } from '@nuxt/types';
import { wrapProperty } from '@nuxtjs/composition-api';
import { Tracer, TracerConfig } from './core/tracer';
import { ImageTraceMessenger } from './core/trace-messenger';
import env from '@/config/env';

/**
 * Context API style
 * @example `const { $tracer } = useContext()`
 */
const TracerPlugin: Plugin = ({ $config }, inject) => {
  const config: TracerConfig = $config.tracer;

  if (!config || !config.appName || !config.applicationName) {
    throw new Error('TracerPlugin: 关键配置不能为空')
  }

  config.sdk = env.TRACER_PLATFORM;

  const $tracer = new Tracer(config);
  $tracer.messenger = new ImageTraceMessenger(config.sdk);
  inject('tracer', $tracer);
};

/**
 * Composition API style
 * @example `const $tracer = useTracer()`
 */
export const useTracer = wrapProperty('$tracer', false);

export default TracerPlugin;


// Nuxt 2.9+
declare module '@nuxt/types' {
  interface Context {
    $tracer: Tracer;
  }

  interface NuxtAppOptions {
    $tracer: Tracer;
  }
}

declare module 'vue/types/vue' {
  interface Vue {
    $tracer: Tracer;
  }
}
