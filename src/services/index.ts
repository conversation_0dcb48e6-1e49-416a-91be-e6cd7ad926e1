import { createUserService } from './user.service';
import { createCompanyService } from './company.service';
import { createAuthService } from './auth.service';
import { createUpdateLogService } from "./update-log.service";
import { HttpClient } from '@/utils/http-client';
import { uuid } from '@/utils/uuid';
import { errorInterceptor } from '@/utils/http-client/interceptors/error.interceptor';

export function createService<T extends HttpClient>(httpClient: T) {
  const servicesMap = {
    user: createUserService(httpClient),
    company: createCompanyService(httpClient),
    auth: createAuthService(httpClient),
    updateLog: createUpdateLogService(httpClient),
  };
  return servicesMap;
}

const httpClient = new HttpClient({
  headers: {
    'content-type': 'application/json',
    'x-requested-with': 'XMLHttpRequest',
    'x-kzz-request-from': 'qcc-rover-web',
    'x-kzz-request-id': uuid(),
  },
});
httpClient.inject(errorInterceptor);

export const service = createService<typeof httpClient>(httpClient);
