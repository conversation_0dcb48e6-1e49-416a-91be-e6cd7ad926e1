import { HttpClient } from '@/utils/http-client';

const AUTH_LOGIN_BY_SMS = '/qcc/user/buser/login/mobile'; // 手机号/验证码登录
const AUTH_LOGIN_BY_PASSWORD = '/qcc/user/buser/login/passwd'; // 手机号/密码登录
const AUTH_VERIFICATION_CODE = '/qcc/user/buser/verificationCode'; // 企业中心-获取手机验证码

export function createAuthService<T extends HttpClient>(httpClient: T) {
  return {
    /**
     * 用户登录-获取手机验证码
     */
    sendVerifyCode(data): Promise<Readonly<any>> {
      return httpClient.post(AUTH_VERIFICATION_CODE, data);
    },

    /**
     * 用户登录-短信登录
     */
    loginBySMS: (params) => {
      return httpClient.post(AUTH_LOGIN_BY_SMS, params);
    },

    /**
     * 用户登录-密码登录
     */
    loginByPassword: (params) => {
      return httpClient.post(AUTH_LOGIN_BY_PASSWORD, params);
    },
  };
}
