import { HttpClient } from '@/utils/http-client';

const USER_PROFILE = '/rover/profile';
const USER_TRIAL_APPLY = '/rover/account/trial/apply';
const USER_RESET_PASSWORD = '/qcc/user/buser/resetPassword';
const USER_TRIAL_VERIFY_CODE = '/rover/account/trial/verificationCodeV3';
const USER_RESET_PASSWORD_VERIFY_CODE = '/qcc/user/buser/sendRestPasswordVerifyCodeV3';

export function createUserService<T extends HttpClient>(httpClient: T) {
  return {
    /**
     * 发送短信验证码
     */
    // bizId: "217017993896023210^0"
    // code: "OK"
    // message: "OK"
    // requestId: "********-AF27-591D-9FB7-07802EDF3639"
    sendVerifyCodeBySMS: (params) =>
      httpClient.post<{
        bizId: string;
        code: string;
        message: string;
        requestId: string;
      }>(USER_TRIAL_VERIFY_CODE, params),

    /**
     * 短信/邮箱验证码(重置密码)
     */
    sendResetPasswordVerifyCodeBySMS: (params) =>
      httpClient.post<{
        code: string;
        phone?: string;
        email?: string;
        sid: string;
        requestId: string;
      }>(USER_RESET_PASSWORD_VERIFY_CODE, params),

    /**
     * 重置密码
     * @param params
     * @returns
     */
    resetPassword: (params) => httpClient.post<{ status: number }>(USER_RESET_PASSWORD, params),

    /**
     * 申请试用
     */
    trialApply: (params: {
      channel: string;
      code: string;
      companyName: string;
      name: string;
      phone: string;
      remarks: string;
    }) => {
      return httpClient.post(USER_TRIAL_APPLY, params);
    },

    /**
     * 用户资料
     * @returns
     */
    profile: () => httpClient.get(USER_PROFILE),
  };
}
