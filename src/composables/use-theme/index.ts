import { onMounted, onUnmounted, ref } from 'vue';
import { throttle } from 'lodash';

export function useTheme<T extends string>(defaultTheme: T) {
  const theme = ref(defaultTheme);
  const setTheme = (themeName) => {
    if (theme.value !== themeName) {
      theme.value = themeName;
    }
  };
  /**
   * 通过滚动事件来判断主题
   * @param event
   */
  const detectThemeByCurrentPosition = (event?: Event) => {
    const y = window.scrollY;
    if (y > 8) {
      setTheme('light');
    } else {
      setTheme('dark');
    }
    event?.stopPropagation();
  };
  const throttledHandler = throttle((ev) => detectThemeByCurrentPosition(ev), 50);

  onMounted(() => {
    detectThemeByCurrentPosition(); // 初始化
    window.addEventListener('scroll', throttledHandler, false);
  });
  onUnmounted(() => window.removeEventListener('scroll', throttledHandler));

  return [theme, setTheme] as const;
}
