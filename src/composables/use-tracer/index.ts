import { Ref, useContext } from "@nuxtjs/composition-api";
import { Route } from "vue-router";

/**
 * 路径匹配页面名称
 */
export const ROUTE_PATH_PAGE_NAME_MAP = {
  '/': '落地页-首页',
  '/portal/terms-of-service': '落地页-服务条款',
  '/portal/about-us': '落地页-服务条款',
  '/portal/reset-password': '落地页-重置密码'
};

/**
 * 跟据路径获取页面名称
 * @param path
 */
const getPageNameByPath = (path: string) => {
  const pageName = ROUTE_PATH_PAGE_NAME_MAP[path];
  if (!pageName) {
    throw new Error('TracerPlugin: 未匹配到页面名称')
  }
  return pageName;
}

/**
 * 抽象埋点插件业务
 */
export const useTracer = (route: Ref<Route>) => {
  const { $tracer } = useContext();

  /**
   * 点击事件
   * @param eventName
   */
  const clickEvent = (eventName: string) => {
    const pageName = getPageNameByPath(route.value.path);
    $tracer.clickEvent(pageName, eventName);
  }

  /**
   * 页面访问
   */
  const pageView = () => {
    const pageName = getPageNameByPath(route.value.path);
    $tracer.pageView(pageName);
  }

  return {
    clickEvent,
    pageView,
  }
}
