@import 'ant-design-vue/lib/button/style/index.less';

@btn-height-base: 32px;
@btn-height-lg: 40px;
@btn-height-sm: 22px;

.ant-btn {
  text-shadow: none;
  box-shadow: none;

  &:hover,
  &:focus {
    color: @base-color-blue-500;
    border-color: @base-color-blue-500;
  }
  &-primary {
    &:hover,
    &:focus {
      color: #fff;
      background-color: @base-color-blue-600;
      border-color: @base-color-blue-600;
    }
  }
  &-link {
    &:hover,
    &:focus {
      border-color: transparent;
    }
  }
  &::after {
    animation: none; // 移除水波纹
  }

  > .anticon + span,
  > span + .anticon {
    margin-left: 5px;
    margin-right: -5px;
  }

  > span + .anticon {
    transform: scale(0.8);
  }
}
