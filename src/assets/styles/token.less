// Colors
// ---------------------------------------------------------------
@base-color-white: #fff;

@base-color-black-800: #000000;
@base-color-black-600: #333333;
@base-color-black-500: #666666; // 前景色
@base-color-black-400: #808080;
@base-color-black-300: #999999;
@base-color-black-200: #bbbbbb;

@base-color-gray-700: #d8d8d8;
@base-color-gray-600: #e3e3e3;
@base-color-gray-500: #eeeeee;
@base-color-gray-400: #f5f5f5;
@base-color-gray-300: #f3f3f3; // 背景色
@base-color-gray-200: #f8f8f8;
@base-color-gray-100: #fbfbfb;

@base-color-blue-700: #336cb4;
@base-color-blue-600: #0069bf; // Hover
@base-color-blue-500: #128bed; // 前景色
@base-color-blue-400: #88c5f6;
@base-color-blue-300: #e5f2fd; // 背景色
@base-color-blue-200: #f2f8fe;
@base-color-blue-100: #fafcff;

@base-color-red-600: #fd485e; // 标找找
@base-color-red-500: #ff6060;
@base-color-red-300: #ffecec;

@base-color-green-500: #00ad65; // 前景色
@base-color-green-300: #e3f6ee; // 背景色

@base-color-yellow-500: #ffaa00;
@base-color-yellow-300: #fff3db;

@base-color-orange-500: #ff722d;
@base-color-orange-300: #ffeee6;

// 灰色
// @base-color-black-500: #666666; // 前景色
// @base-color-gray-300: #f3f3f3; // 背景色

// 蓝紫色
@base-color-violet-500: #845fff;
@base-color-violet-300: #f0ebff;

// 钴蓝
@base-color-zaffer-500: #6171ff;
@base-color-zaffer-300: #edeeff;

// 科技蓝
@base-color-scifi-500: #367dff;
@base-color-scifi-300: #e9f1ff;

// 金色
@base-color-cyan-500: #00a3cc;
@base-color-cyan-300: #dff3f8;

// 金色
@base-color-gold-500: #bb833d;
@base-color-gold-300: #f6f0e7;

// Text size
// ---------------------------------------------------------------
@base-text-xs: 12px;
@base-text-sm: 13px;
@base-text-md: 14px;
@base-text-lg: 16px;
@base-text-xl: 18px;
@base-text-2xl: 20px;
@base-text-3xl: 24px;

// Font weight
// ---------------------------------------------------------------
@base-font-light: 300;
@base-font-normal: 400;
@base-font-medium: 500;
@base-font-bold: 700;

// Line height
// ---------------------------------------------------------------
@base-leading-1: 18px;
@base-leading-2: 20px;
@base-leading-3: 22px;
@base-leading-4: 24px;
@base-leading-5: 26px;
@base-leading-6: 28px;
@base-leading-7: 30px;

// Mixins
// ---------------------------------------------------------------
@base-table-base-font-size: @base-text-sm;
@base-table-base-cell-gap: 9px 10px;
@base-table-header-bg: @base-color-blue-200;
@base-table-border-color: #e4eef6;
@base-table-row-hover-bg: #f2f8fe;
