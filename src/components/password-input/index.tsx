import { Icon, Input } from 'ant-design-vue';
import { defineComponent, ref, type PropType } from 'vue';
import styles from './styles.module.less';

type InputProps = InstanceType<typeof Input>['$props'];
const props = {
  value: {
    type: String as PropType<string>,
    required: false,
  },
};
type Props = typeof props & InputProps;
type InputMode = 'password' | 'text';

const PasswordInput = defineComponent({
  name: 'PasswordInput',
  inheritAttrs: false, // 不自动透传 attrs, 通过 `attrs` 属性手动透传
  model: {
    prop: 'value',
    event: 'change.value',
  },
  props: props as Props,
  emits: ['change.value'],
  setup() {
    const mode = ref<InputMode>('password');
    const handleSwitchMode = () => {
      mode.value = mode.value === 'password' ? 'text' : 'password';
    };
    return {
      mode,
      handleSwitchMode,
    };
  },
  render() {
    return (
      <Input
        {...{
          props: this.$props,
          attrs: this.$attrs,
          on: this.$listeners,
        }}
        class={styles.input}
        type={this.mode}
      >
        <span
          class={{
            [styles.action]: true,
            [styles.active]: this.mode === 'text',
          }}
          slot="suffix"
          onClick={this.handleSwitchMode}
        >
          <Icon type="eye" />
        </span>
      </Input>
    );
  },
});

export default PasswordInput;
