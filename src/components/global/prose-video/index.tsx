import { defineComponent } from 'vue';

const ProseVideo = defineComponent({
  name: 'ProseVideo',
  props: {
    src: {
      type: String,
      required: true,
    },
  },
  setup(props) {
    const handleAutoPlay = (event) => {
      event?.target?.play();
    };
    return {
      handleAutoPlay,
    };
  },
  render() {
    return (
      <video ref="video" src={this.src} width="100%" height="100%" controls onCanplay={this.handleAutoPlay}>
        抱歉，您的浏览器不支持内嵌视频，不过不用担心，你可以
        <a href={this.src}>下载</a>
        并用你喜欢的播放器观看！
      </video>
    );
  },
});

export default ProseVideo;
