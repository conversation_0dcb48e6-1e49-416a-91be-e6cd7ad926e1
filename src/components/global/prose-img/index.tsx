import { defineComponent, onMounted, ref } from 'vue';
import mediumZoom from 'medium-zoom'
import styles from './style.module.less';

const ProseImg = defineComponent({
  name: 'ProseImg',
  props: {
    src: {
      type: String,
      required: true,
    },
  },
  setup() {
    const imgRef = ref<HTMLImageElement | null>(null);
    const zoom = ref(null)

    onMounted(() => {
      zoom.value = mediumZoom(imgRef.value, {
        background: 'rgba(0,0,0,0.45)',
        margin: 16
      })
    })

    return {
      imgRef,
      zoom
    };
  },
  render() {
    return (
        <img class={styles.content} ref="imgRef" src={this.src} onClick={() => this.zoom.open()} />
    );
  },
});

export default ProseImg;
