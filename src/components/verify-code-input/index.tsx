import { Input } from 'ant-design-vue';
import { defineComponent, ref, type PropType, computed } from 'vue';
import styles from './styles.module.less';

type InputProps = InstanceType<typeof Input>['$props'];
const props = {
  value: {
    type: String as PropType<string>,
    required: false,
  },
  sender: {
    type: Function as PropType<() => Promise<any>>,
    required: true,
  },
};
type Props = typeof props & InputProps;

const INTERVAL_SECONDS = 60;
const VerifyCodeInput = defineComponent({
  name: 'VerifyCodeInput',
  inheritAttrs: false, // 不自动透传 attrs, 通过 `attrs` 属性手动透传
  model: {
    prop: 'value',
    event: 'change.value',
  },
  props: props as Props,
  emits: ['change.value'],
  setup(_props) {
    const counter = ref(INTERVAL_SECONDS);
    const isCounting = ref(false);
    const countingText = computed(() => {
      if (counter.value > 0 && counter.value < INTERVAL_SECONDS) {
        return `${counter.value}s 重新发送`;
      }
      return '获取验证码';
    });

    const reset = () => {
      counter.value = INTERVAL_SECONDS;
    };
    reset();

    /**
     * 编写倒计时函数, 60秒后可以重发，每秒钟更新一次
     */
    const counting = () => {
      return new Promise((resolve) => {
        counter.value -= 1;
        const timer = setInterval(() => {
          counter.value -= 1;
          if (counter.value <= 0) {
            clearInterval(timer);
            resolve(null);
          }
        }, 1000);
      });
    };

    /**
     * 发送短信验证码期间禁止点击
     */
    const handleSendCode = async () => {
      if (isCounting.value) {
        return;
      }
      try {
        await _props.sender();
        isCounting.value = true;
        await counting();
        isCounting.value = false;
        reset(); // 重置计时器
      } catch (error) {
        if (error) {
          console.error(error);
        }
      }
    };

    return {
      countingText,
      handleSendCode,
    };
  },
  render() {
    return (
      <div>
        <Input
          {...{
            props: this.$props,
            attrs: this.$attrs,
            on: this.$listeners,
          }}
          class={[styles.input, styles.verifyCode]}
        >
          <a
            class={{
              [styles.action]: true,
              [styles.disable]: this.isCounting,
            }}
            slot="suffix"
            onClick={this.handleSendCode}
          >
            {this.countingText}
          </a>
        </Input>
      </div>
    );
  },
});

export default VerifyCodeInput;
