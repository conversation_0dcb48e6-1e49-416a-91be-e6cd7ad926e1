import { ref } from 'vue';

export const useNoCaptcha = () => {
  const verifyToken = ref<string>();

  const isValid = ref(false);

  /**
 * 人机验证回调
 */
  const validator = (captchaVerifyParam: string) => {
    if (captchaVerifyParam) {
      verifyToken.value = captchaVerifyParam;
      isValid.value = true;
    } else {
      isValid.value = false;
      // eslint-disable-next-line no-console
      console.error('人机验证未通过 pass');
    }
  };

  return [
    verifyToken,
    isValid,
    validator
  ] as const;
}
