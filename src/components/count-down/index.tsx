import { PropType, computed, defineComponent, ref } from '@nuxtjs/composition-api';
import { Button } from 'ant-design-vue';

const CountDown = defineComponent({
  name: 'CountDown',
  props: {
    value: {
      type: Number,
      default: 60,
    },
    placeholder: {
      type: String,
      required: true,
    },
    format: {
      type: String,
      default: '{{count}}秒后重发',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    /**
     * 计时前检查
     */
    action: {
      type: Function as PropType<() => Promise<boolean>>,
      required: false,
    },
  },
  setup(props) {
    const timer = ref<ReturnType<typeof setInterval>>();
    const count = ref(props.value);
    const ticking = ref(false);

    const start = async () => {
      if (typeof props.action === 'function') {
        try {
          const canStart = (await props.action?.()) || false;
          if (canStart) {
            timer.value = setInterval(() => {
              count.value -= 1;
              if (count.value === 0) {
                clearInterval(timer.value);
                count.value = props.value;
              }
            }, 1000);
          }
        } catch (error) {
          console.error(error);
        }
      }
    };

    const baned = computed(() => {
      return props.disabled || count.value !== props.value;
    });

    const label = computed(() => {
      // const formated = template(this.format)({
      //   count: this.count,
      // });
      // return this.isDisabled ? formated : this.title;
      // return baned ? props.placeholder : props.format.replace('{{count}}', count.value);
      // return baned ? props.placeholder : count.value;
      const counting = props.format.replace('{{count}}', `${count.value}`);
      return count.value < props.value ? counting : props.placeholder;
    });
    return {
      label,

      baned,
      ticking,
      start,
    };
  },
  render() {
    return (
      <Button disabled={this.baned} type="link" onClick={this.start}>
        {this.label}
      </Button>
    );
  },
});

export default CountDown;
