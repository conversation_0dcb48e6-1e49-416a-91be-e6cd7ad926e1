import { computed, ref, type Ref } from 'vue';

export enum STATE {
  idle = 0, // 初始状态
  loading = 1, // 加载中
  loaded = 2, // 加载成功
  failed = 3, // 加载失败
}

export const useFetchState = <P extends Record<string, unknown> | undefined, R = unknown>(
  request: (params?: P) => Promise<R>
) => {
  const state = ref(STATE.idle); // 默认为初始状态
  const result: Ref<R | null> = ref(null);
  const error: Ref<any | null> = ref(null);

  const isIdle = computed(() => state.value === STATE.idle);
  const isLoading = computed(() => state.value === STATE.loading);
  const isLoaded = computed(() => state.value === STATE.loaded);
  const isFailed = computed(() => state.value === STATE.failed);

  const execute = async (params?: P) => {
    // console.log(params);
    state.value = STATE.loading;
    error.value = null;
    try {
      result.value = await request(params);
      state.value = STATE.loaded;
    } catch (err) {
      error.value = err;
      result.value = null;
      state.value = STATE.failed;
    }
  };

  return {
    state,
    result,
    error,
    execute,
    isIdle,
    isLoading,
    isLoaded,
    isFailed,
  };
};
