import { AxiosInstance } from 'axios';

export const errorInterceptor = (instance: AxiosInstance): void => {
  instance.interceptors.response.use(undefined, (error) => {
    const { response, isAxiosError } = error;
    // NOTE: 是 AxiosError 同时 response 为空，则判定为海外IP拦截策略
    const isOverseaIpBlocked = isAxiosError && !response;

    if (isOverseaIpBlocked) {
      const errorResponse = {
        data: {
          code: 302,
          error: `当前登录失败，可能是由于海外IP访问限制或其他系统问题导致，建议稍后重试。若持续异常，请联系客服协助处理。\n\n客服热线：400-088-8275 客服工作时间：周一至周五（09:00-20:00）`,
          statusCode: 302,
          timestamp: new Date().toISOString(),
        }
      }
      const customError = new Error(errorResponse.data.error);
      Object.assign(customError, {
        ...error,
        response: errorResponse
      });
      return Promise.reject(customError);
    }
    return Promise.reject(error);
  });
}
