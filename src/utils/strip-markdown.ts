export const stripMarkdown = (text: string) => {
  if (!text) return '';

  // 首先处理转义字符 - 将它们替换为临时占位符
  const escapedChars = text.match(/\\[-.#*+()[\]{}]/g) || [];
  const placeholders = [];

  escapedChars.forEach((char, index) => {
    placeholders.push(char);
    text = text.replace(char, `ESCAPEDPLACEHOLDER${index}`);
  });
  // 移除Markdown标题
  text = text.replace(/^#+\s+/gm, '');
  // 移除无序列表标记
  text = text.replace(/^[-*+]\s+/gm, '');
  // 移除有序列表标记
  text = text.replace(/^\d+\.\s+/gm, '');
  // 移除粗体和斜体
  text = text.replace(/(\*\*|__)(.*?)\1/g, '$2');
  text = text.replace(/(\*|_)(.*?)\1/g, '$2');
  // 移除代码块
  text = text.replace(/```[\s\S]*?```/g, '');
  // 移除行内代码
  text = text.replace(/`([^`]+)`/g, '$1');
  // 移除链接，但保留链接文字
  text = text.replace(/\[([^\]]+)\]\([^)]+\)/g, '$1');
  // 移除图片，但保留替代文本
  text = text.replace(/!\[([^\]]+)\]\([^)]+\)/g, '$1');
  // 移除HTML标签
  text = text.replace(/<[^>]+>/g, '');
  // 恢复转义字符
  placeholders.forEach((placeholder, index) => {
    // 移除转义符号，只保留字符本身
    const unescapedChar = placeholder.substring(1);
    text = text.replace(`ESCAPEDPLACEHOLDER${index}`, unescapedChar);
  });
  // 清理多余的空格和换行
  text = text.replace(/\n\s*\n/g, '\n').trim();
  text = text.replace(/\s+/g, ' ');

  return text;
};
