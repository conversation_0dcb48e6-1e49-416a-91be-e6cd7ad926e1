{"extends": "./tsconfig.base.json", "compilerOptions": {"target": "ES2018", "lib": ["ESNext", "ESNext.AsyncIterable", "DOM"], "types": ["@nuxt/types", "@nuxt/typescript-build", "@nuxtjs/axios", "@nuxtjs/sentry", "@types/node", "@nuxt/content"], "allowJs": true, "sourceMap": true, "noEmit": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["nuxt.config.ts", "./src/**/*", "./src/**/*.vue", "./build/**/*", "scripts/update-md-date.mjs"], "vueCompilerOptions": {"target": 2.7}}